from pydantic import BaseModel, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

class AssistantCreate(BaseModel):
    """Model for creating a new assistant"""
    store_id: int
    assistant_name: Optional[str] = None  # Will be auto-generated if not provided

    @field_validator('assistant_name')
    @classmethod
    def validate_assistant_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate assistant name format"""
        if v is not None:
            if not v.replace('-', '').replace('_', '').isalnum():
                raise ValueError('Assistant name must be alphanumeric with hyphens and underscores allowed')
            if len(v) < 3:
                raise ValueError('Assistant name must be at least 3 characters long')
            if len(v) > 100:
                raise ValueError('Assistant name must be less than 100 characters')
        return v

class AssistantUpdate(BaseModel):
    """Model for updating assistant information"""
    status: Optional[str] = None

    @field_validator('status')
    @classmethod
    def validate_status(cls, v: Optional[str]) -> Optional[str]:
        """Validate assistant status"""
        if v is not None:
            allowed_statuses = ['active', 'inactive', 'error', 'creating', 'syncing']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {", ".join(allowed_statuses)}')
        return v

class AssistantResponse(BaseModel):
    """Model for assistant response"""
    id: int
    store_id: int
    assistant_name: str
    pinecone_assistant_id: Optional[str]
    status: str
    last_sync: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]

    # Data file tracking
    products_file_id: Optional[str]
    orders_file_id: Optional[str]
    customers_file_id: Optional[str]

    class Config:
        from_attributes = True

class AssistantWithStore(AssistantResponse):
    """Assistant response with store information"""
    store_name: Optional[str] = None
    shop_url: Optional[str] = None
    store_status: Optional[str] = None

class ChatRequest(BaseModel):
    """Model for chat requests"""
    message: str
    store_id: Optional[int] = None  # Store selection for multi-store users
    conversation_id: Optional[str] = None

    @field_validator('message')
    @classmethod
    def validate_message(cls, v: str) -> str:
        """Validate chat message"""
        v = v.strip()
        if not v:
            raise ValueError('Message cannot be empty')
        if len(v) > 2000:
            raise ValueError('Message too long (max 2000 characters)')
        return v

class ChatResponse(BaseModel):
    """Model for chat responses"""
    response: str
    conversation_id: str
    store_id: int
    store_name: Optional[str] = None
    assistant_name: str
    timestamp: datetime = datetime.now(timezone.utc)

class StoreSelection(BaseModel):
    """Model for store selection"""
    store_id: int
    store_name: str
    shop_url: str
    assistant_status: str
    has_assistant: bool

class AssistantStats(BaseModel):
    """Model for assistant statistics"""
    total_conversations: int = 0
    total_messages: int = 0
    last_conversation: Optional[datetime] = None
    data_files_count: int = 0
    last_data_sync: Optional[datetime] = None

class AssistantWithStats(AssistantResponse):
    """Assistant response with statistics"""
    stats: Optional[AssistantStats] = None
    store_name: Optional[str] = None
    shop_url: Optional[str] = None

class DataSyncRequest(BaseModel):
    """Model for data synchronization requests"""
    store_id: int
    sync_products: bool = True
    sync_orders: bool = True
    sync_customers: bool = True
    force_sync: bool = False

class DataSyncResult(BaseModel):
    """Model for data synchronization results"""
    success: bool
    message: str
    files_updated: List[str] = []
    files_created: List[str] = []
    errors: List[str] = []
    sync_duration: Optional[float] = None

class BulkAssistantOperation(BaseModel):
    """Model for bulk assistant operations"""
    assistant_ids: List[int]
    operation: str  # 'sync', 'activate', 'deactivate', 'delete'

    @field_validator('operation')
    @classmethod
    def validate_operation(cls, v: str) -> str:
        """Validate bulk operation type"""
        allowed_operations = ['sync', 'activate', 'deactivate', 'delete']
        if v not in allowed_operations:
            raise ValueError(f'Operation must be one of: {", ".join(allowed_operations)}')
        return v

class ConversationHistory(BaseModel):
    """Model for conversation history"""
    conversation_id: str
    store_id: int
    store_name: Optional[str]
    messages: List[Dict[str, Any]]
    created_at: datetime
    last_message_at: datetime
    message_count: int

class AssistantHealth(BaseModel):
    """Model for assistant health check"""
    assistant_id: int
    assistant_name: str
    store_id: int
    healthy: bool
    status: str
    last_sync: Optional[datetime]
    pinecone_status: Optional[str]
    data_files_status: Dict[str, bool] = {}
    error_details: Optional[str] = None
