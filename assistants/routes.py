from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from database.session import get_db
from database.models import User
from database.crud import StoreCRUD, AssistantCRUD
from auth.dependencies import get_current_active_user
from .models import (
    <PERSON><PERSON><PERSON>, Assistant<PERSON>p<PERSON>, AssistantResponse, AssistantWithStore,
    ChatRequest, ChatResponse, StoreSelection, AssistantWithStats,
    DataSyncRequest, DataSyncResult, BulkAssistantOperation, AssistantHealth
)
from .multi_store_assistant import multi_store_manager

router = APIRouter(prefix="/assistants", tags=["assistants"])

@router.post("/", response_model=AssistantResponse, status_code=status.HTTP_201_CREATED)
async def create_assistant(
    assistant_data: AssistantCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new assistant for a store
    """
    # Verify store ownership
    store = StoreCRUD.get_store_by_id(db, assistant_data.store_id)
    if not store or store.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")
    
    try:
        assistant = multi_store_manager.create_assistant_for_store(db, store)
        return AssistantResponse.from_orm(assistant)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get("/", response_model=List[AssistantWithStore])
async def get_user_assistants(
    include_stats: bool = Query(False, description="Include assistant statistics"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all assistants for the current user
    """
    assistants = multi_store_manager.get_user_assistants(db, current_user.id)
    
    result = []
    for assistant in assistants:
        assistant_data = AssistantWithStore.from_orm(assistant)
        
        if include_stats:
            stats = multi_store_manager.get_assistant_stats(db, assistant.id)
            assistant_data.stats = stats
        
        result.append(assistant_data)
    
    return result

@router.get("/{assistant_id}", response_model=AssistantWithStats)
async def get_assistant(
    assistant_id: int,
    include_stats: bool = Query(True, description="Include assistant statistics"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific assistant by ID
    """
    assistant = AssistantCRUD.get_assistant_by_store(db, assistant_id)
    if not assistant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    # Verify ownership through store
    store = StoreCRUD.get_store_by_id(db, assistant.store_id)
    if not store or store.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    assistant_data = AssistantWithStats.from_orm(assistant)
    assistant_data.store_name = store.store_name
    assistant_data.shop_url = store.shop_url
    
    if include_stats:
        stats = multi_store_manager.get_assistant_stats(db, assistant.id)
        assistant_data.stats = stats
    
    return assistant_data

@router.put("/{assistant_id}", response_model=AssistantResponse)
async def update_assistant(
    assistant_id: int,
    update_data: AssistantUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update assistant information
    """
    assistant = AssistantCRUD.get_assistant_by_store(db, assistant_id)
    if not assistant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    # Verify ownership through store
    store = StoreCRUD.get_store_by_id(db, assistant.store_id)
    if not store or store.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    update_dict = update_data.dict(exclude_unset=True)
    updated_assistant = AssistantCRUD.update_assistant(db, assistant.id, **update_dict)
    
    if not updated_assistant:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update assistant")
    
    return AssistantResponse.from_orm(updated_assistant)

@router.delete("/{assistant_id}")
async def delete_assistant(
    assistant_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete an assistant
    """
    assistant = AssistantCRUD.get_assistant_by_store(db, assistant_id)
    if not assistant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    # Verify ownership through store
    store = StoreCRUD.get_store_by_id(db, assistant.store_id)
    if not store or store.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    success = multi_store_manager.delete_assistant(db, store.id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete assistant")
    
    return {"message": "Assistant deleted successfully"}

@router.post("/chat", response_model=ChatResponse)
async def chat_with_assistant(
    chat_request: ChatRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Chat with a store's assistant
    """
    if not chat_request.store_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Store ID is required")
    
    try:
        response, conversation_id = multi_store_manager.chat_with_store_assistant(
            db=db,
            store_id=chat_request.store_id,
            user_id=current_user.id,
            message=chat_request.message,
            conversation_id=chat_request.conversation_id
        )
        
        # Get store and assistant info for response
        store = StoreCRUD.get_store_by_id(db, chat_request.store_id)
        assistant = AssistantCRUD.get_assistant_by_store(db, chat_request.store_id)
        
        return ChatResponse(
            response=response,
            conversation_id=conversation_id,
            store_id=chat_request.store_id,
            store_name=store.store_name if store else None,
            assistant_name=assistant.assistant_name if assistant else "Assistant"
        )
        
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get("/stores/selection", response_model=List[StoreSelection])
async def get_store_selection(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get store selection options for chat
    """
    stores = StoreCRUD.get_stores_by_user(db, current_user.id)
    
    selections = []
    for store in stores:
        assistant = AssistantCRUD.get_assistant_by_store(db, store.id)
        
        selections.append(StoreSelection(
            store_id=store.id,
            store_name=store.store_name or store.shop_url,
            shop_url=store.shop_url,
            assistant_status=assistant.status if assistant else "not_created",
            has_assistant=assistant is not None
        ))
    
    return selections

@router.post("/{assistant_id}/sync", response_model=DataSyncResult)
async def sync_assistant_data(
    assistant_id: int,
    sync_request: Optional[DataSyncRequest] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Sync assistant data from Shopify
    """
    assistant = AssistantCRUD.get_assistant_by_store(db, assistant_id)
    if not assistant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    # Verify ownership through store
    store = StoreCRUD.get_store_by_id(db, assistant.store_id)
    if not store or store.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    force_sync = sync_request.force_sync if sync_request else False
    
    try:
        result = multi_store_manager.sync_store_data(
            db=db,
            store_id=assistant.store_id,
            user_id=current_user.id,
            force=force_sync
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.post("/bulk-operation")
async def bulk_assistant_operation(
    operation_data: BulkAssistantOperation,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Perform bulk operations on multiple assistants
    """
    results = {
        'success': [],
        'failed': [],
        'total': len(operation_data.assistant_ids)
    }
    
    for assistant_id in operation_data.assistant_ids:
        try:
            assistant = AssistantCRUD.get_assistant_by_store(db, assistant_id)
            if not assistant:
                results['failed'].append(assistant_id)
                continue
            
            # Verify ownership
            store = StoreCRUD.get_store_by_id(db, assistant.store_id)
            if not store or store.user_id != current_user.id:
                results['failed'].append(assistant_id)
                continue
            
            success = False
            if operation_data.operation == 'sync':
                sync_result = multi_store_manager.sync_store_data(db, store.id, current_user.id)
                success = sync_result.success
            elif operation_data.operation == 'activate':
                updated = AssistantCRUD.update_assistant(db, assistant.id, status='active')
                success = updated is not None
            elif operation_data.operation == 'deactivate':
                updated = AssistantCRUD.update_assistant(db, assistant.id, status='inactive')
                success = updated is not None
            elif operation_data.operation == 'delete':
                success = multi_store_manager.delete_assistant(db, store.id, current_user.id)
            
            if success:
                results['success'].append(assistant_id)
            else:
                results['failed'].append(assistant_id)
                
        except Exception:
            results['failed'].append(assistant_id)
    
    return results

@router.get("/{assistant_id}/health", response_model=AssistantHealth)
async def check_assistant_health(
    assistant_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Check assistant health status
    """
    assistant = AssistantCRUD.get_assistant_by_store(db, assistant_id)
    if not assistant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    # Verify ownership through store
    store = StoreCRUD.get_store_by_id(db, assistant.store_id)
    if not store or store.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")
    
    try:
        # Try to get assistant instance
        pinecone_assistant = multi_store_manager.get_assistant_for_store(db, store.id, current_user.id)
        
        healthy = pinecone_assistant is not None and assistant.status == 'active'
        
        return AssistantHealth(
            assistant_id=assistant.id,
            assistant_name=assistant.assistant_name,
            store_id=assistant.store_id,
            healthy=healthy,
            status=assistant.status,
            last_sync=assistant.last_sync,
            pinecone_status="connected" if pinecone_assistant else "disconnected",
            data_files_status={
                "products": assistant.products_file_id is not None,
                "orders": assistant.orders_file_id is not None,
                "customers": assistant.customers_file_id is not None
            }
        )
        
    except Exception as e:
        return AssistantHealth(
            assistant_id=assistant.id,
            assistant_name=assistant.assistant_name,
            store_id=assistant.store_id,
            healthy=False,
            status="error",
            last_sync=assistant.last_sync,
            pinecone_status="error",
            error_details=str(e)
        )
