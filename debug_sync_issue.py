#!/usr/bin/env python3
"""
Debug script to investigate the sync duplicate issue.
This script will help us understand what's happening during the sync process.
"""

import os
import sys
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_assistant_files(assistant_name):
    """Debug function to show all files in an assistant."""
    try:
        from pinecone_assistant import PineconeAssistant
        
        # Create assistant instance
        assistant = PineconeAssistant(assistant_name=assistant_name)
        
        if not assistant.assistant:
            print(f"Assistant {assistant_name} not found")
            return
        
        # List all files
        files = assistant.assistant.list_files()
        
        print(f"\n=== FILES IN ASSISTANT '{assistant_name}' ===")
        print(f"Total files: {len(files)}")
        
        # Group files by type
        file_groups = {}
        for file in files:
            if file.name.startswith("formatted_"):
                parts = file.name.replace("formatted_", "").replace(".txt", "").split("_part_")
                if len(parts) == 2:
                    data_type = parts[0]
                    chunk_num = parts[1]
                    key = f"{data_type}_part_{chunk_num}"
                elif len(parts) == 1:
                    data_type = parts[0]
                    key = f"{data_type}_single"
                else:
                    key = "unknown"
                
                if key not in file_groups:
                    file_groups[key] = []
                file_groups[key].append(file)
        
        # Show files grouped
        for key, file_list in sorted(file_groups.items()):
            print(f"\n--- {key} ---")
            for file in file_list:
                print(f"  {file.name}")
                print(f"    ID: {file.id}")
                print(f"    Created: {file.created_on}")
                print(f"    Status: {file.status}")
                if hasattr(file, 'metadata') and file.metadata:
                    chunk_hash = file.metadata.get('chunk_hash', 'N/A')
                    print(f"    Hash: {chunk_hash}")
                
                if len(file_list) > 1:
                    print(f"    ⚠️  DUPLICATE DETECTED!")
        
        # Check for duplicates
        duplicates = {key: files for key, files in file_groups.items() if len(files) > 1}
        if duplicates:
            print(f"\n🚨 FOUND {len(duplicates)} DUPLICATE FILE GROUPS:")
            for key, files in duplicates.items():
                print(f"  - {key}: {len(files)} copies")
        else:
            print(f"\n✅ No duplicate files found")
            
    except Exception as e:
        print(f"Error debugging assistant files: {str(e)}")
        import traceback
        traceback.print_exc()

def debug_database_hashes(store_id):
    """Debug function to show database hashes for a store."""
    try:
        from database.session import SessionLocal
        from database.crud import AssistantCRUD
        
        db = SessionLocal()
        assistant_record = AssistantCRUD.get_assistant_by_store(db, store_id)
        
        if assistant_record:
            print(f"\n=== DATABASE HASHES FOR STORE {store_id} ===")
            print(f"Assistant: {assistant_record.assistant_name}")
            print(f"Status: {assistant_record.status}")
            print(f"Products hash: {assistant_record.products_hash[:8] + '...' if assistant_record.products_hash else 'None'}")
            print(f"Orders hash: {assistant_record.orders_hash[:8] + '...' if assistant_record.orders_hash else 'None'}")
            print(f"Customers hash: {assistant_record.customers_hash[:8] + '...' if assistant_record.customers_hash else 'None'}")
            print(f"Last sync: {assistant_record.last_sync}")
        else:
            print(f"No assistant found for store {store_id}")
            
        db.close()
        
    except Exception as e:
        print(f"Error debugging database hashes: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main debug function."""
    print("🔍 DEBUGGING SYNC DUPLICATE ISSUE")
    print("=" * 50)
    
    try:
        from database.session import SessionLocal
        from database.crud import StoreCRUD
        
        db = SessionLocal()
        
        # Get all stores
        stores = StoreCRUD.get_all_stores(db)
        if not stores:
            print("No stores found in database")
            return
        
        print(f"Found {len(stores)} stores:")
        for i, store in enumerate(stores):
            print(f"{i+1}. {store.store_name} (ID: {store.id})")
        
        # Use the first store for debugging
        test_store = stores[0]
        print(f"\nUsing store: {test_store.store_name} (ID: {test_store.id})")
        
        # Debug database hashes
        debug_database_hashes(test_store.id)
        
        # Get assistant name
        from assistants.multi_store_assistant import multi_store_manager
        assistant_record = multi_store_manager.create_assistant_for_store(db, test_store)
        
        if assistant_record:
            # Debug assistant files
            debug_assistant_files(assistant_record.assistant_name)
        
        db.close()
        
    except Exception as e:
        print(f"Debug failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
