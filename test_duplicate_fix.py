#!/usr/bin/env python3
"""
Test script to verify the duplicate file fix is working.
"""

import os
import sys

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_duplicate_fix():
    """Test that the duplicate file fix is working."""
    print("🧪 TESTING DUPLICATE FILE FIX")
    print("=" * 50)
    
    try:
        from database.session import SessionLocal
        from database.crud import StoreCRUD, AssistantCRUD
        from assistants.multi_store_assistant import multi_store_manager
        
        db = SessionLocal()
        
        # Get first store
        stores = StoreCRUD.get_all_stores(db)
        if not stores:
            print("❌ No stores found")
            return False
            
        test_store = stores[0]
        print(f"📍 Using store: {test_store.store_name} (ID: {test_store.id})")
        
        # Get or create assistant
        assistant_record = AssistantCRUD.get_assistant_by_store(db, test_store.id)
        if not assistant_record:
            print("🔧 Creating new assistant...")
            assistant_record = multi_store_manager.create_assistant_for_store(db, test_store)
        
        if not assistant_record:
            print("❌ Failed to get/create assistant")
            return False
            
        print(f"🤖 Assistant: {assistant_record.assistant_name}")
        
        # Get Pinecone assistant instance
        pinecone_assistant = multi_store_manager.get_assistant_for_store(db, test_store.id, test_store.user_id)
        if not pinecone_assistant:
            print("❌ Failed to get Pinecone assistant instance")
            return False
        
        # Count files before sync
        files_before = pinecone_assistant.assistant.list_files()
        print(f"📁 Files before sync: {len(files_before)}")
        
        # Show file details
        for file in files_before:
            print(f"   - {file.name} (created: {file.created_on})")
        
        # Perform sync
        print("\n🔄 Performing sync...")
        sync_result = multi_store_manager.sync_store_data(
            db=db,
            store_id=test_store.id,
            user_id=test_store.user_id,
            force=False
        )
        
        print(f"✅ Sync result: {sync_result.message}")
        
        # Count files after sync
        files_after = pinecone_assistant.assistant.list_files()
        print(f"📁 Files after sync: {len(files_after)}")
        
        # Check for duplicates
        file_names = [file.name for file in files_after]
        unique_names = set(file_names)
        
        if len(file_names) == len(unique_names):
            print("✅ No duplicate files found!")
            success = True
        else:
            print("❌ Duplicate files detected:")
            for name in file_names:
                count = file_names.count(name)
                if count > 1:
                    print(f"   - {name}: {count} copies")
            success = False
        
        # Check if file count changed
        if len(files_before) == len(files_after):
            print("✅ File count remained stable")
        else:
            print(f"⚠️  File count changed: {len(files_before)} -> {len(files_after)}")
            if len(files_after) > len(files_before):
                print("   This might indicate duplicates were created")
                success = False
        
        db.close()
        return success
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    success = test_duplicate_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 TEST PASSED: Duplicate file fix is working!")
    else:
        print("💥 TEST FAILED: Duplicate files were still created")
    print("=" * 50)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
