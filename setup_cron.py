#!/usr/bin/env python3
"""
Cron Setup and Management Script

This script helps set up, manage, and monitor cron jobs for auto-sync functionality.

Usage:
    python setup_cron.py install    # Install cron job
    python setup_cron.py remove     # Remove cron job
    python setup_cron.py status     # Check cron job status
    python setup_cron.py test       # Test sync script
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CronManager:
    """Manages cron jobs for auto-sync functionality"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.script_path = self.project_root / 'auto_sync_cron.py'
        self.log_dir = self.project_root / 'logs' / 'cron'
        self.log_file = self.log_dir / 'auto_sync_cron.log'
        
        # Ensure log directory exists
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Cron job configuration
        self.cron_schedule = '0 0,12 * * *'  # Every 12 hours at 12:00 AM and 12:00 PM
        self.cron_command = f'cd {self.project_root} && python {self.script_path} >> {self.log_file} 2>&1'
        self.cron_entry = f'{self.cron_schedule} {self.cron_command}'
        self.cron_comment = '# Pinecone E-commerce Assistant Auto-Sync'
    
    def install_cron_job(self) -> bool:
        """Install the cron job"""
        try:
            logger.info("Installing cron job for auto-sync...")
            
            # Check if script exists
            if not self.script_path.exists():
                logger.error(f"Auto-sync script not found: {self.script_path}")
                return False
            
            # Get current crontab
            try:
                current_crontab = subprocess.check_output(['crontab', '-l'], 
                                                        stderr=subprocess.DEVNULL).decode('utf-8')
            except subprocess.CalledProcessError:
                # No crontab exists yet
                current_crontab = ""
            
            # Check if our job already exists
            if self.cron_comment in current_crontab or str(self.script_path) in current_crontab:
                logger.warning("Cron job already exists. Use 'remove' first to reinstall.")
                return False
            
            # Add our cron job
            new_crontab = current_crontab.rstrip() + '\n' if current_crontab else ''
            new_crontab += f'{self.cron_comment}\n{self.cron_entry}\n'
            
            # Install new crontab
            process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE)
            process.communicate(input=new_crontab.encode('utf-8'))
            
            if process.returncode == 0:
                logger.info("✅ Cron job installed successfully!")
                logger.info(f"📅 Schedule: Every 12 hours at 12:00 AM and 12:00 PM UTC")
                logger.info(f"📝 Log file: {self.log_file}")
                logger.info(f"🔧 Command: {self.cron_command}")
                return True
            else:
                logger.error("❌ Failed to install cron job")
                return False
                
        except Exception as e:
            logger.error(f"Error installing cron job: {str(e)}")
            return False
    
    def remove_cron_job(self) -> bool:
        """Remove the cron job"""
        try:
            logger.info("Removing cron job for auto-sync...")
            
            # Get current crontab
            try:
                current_crontab = subprocess.check_output(['crontab', '-l']).decode('utf-8')
            except subprocess.CalledProcessError:
                logger.info("No crontab found")
                return True
            
            # Remove our cron job lines
            lines = current_crontab.split('\n')
            new_lines = []
            skip_next = False
            
            for line in lines:
                if skip_next:
                    skip_next = False
                    continue
                    
                if self.cron_comment in line:
                    skip_next = True  # Skip the next line (the actual cron entry)
                    continue
                    
                if str(self.script_path) in line:
                    continue  # Skip lines containing our script path
                    
                new_lines.append(line)
            
            # Install updated crontab
            new_crontab = '\n'.join(new_lines)
            process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE)
            process.communicate(input=new_crontab.encode('utf-8'))
            
            if process.returncode == 0:
                logger.info("✅ Cron job removed successfully!")
                return True
            else:
                logger.error("❌ Failed to remove cron job")
                return False
                
        except Exception as e:
            logger.error(f"Error removing cron job: {str(e)}")
            return False
    
    def check_cron_status(self) -> dict:
        """Check the status of the cron job"""
        try:
            logger.info("Checking cron job status...")
            
            # Check if crontab exists and contains our job
            try:
                current_crontab = subprocess.check_output(['crontab', '-l']).decode('utf-8')
                job_exists = (self.cron_comment in current_crontab or 
                            str(self.script_path) in current_crontab)
            except subprocess.CalledProcessError:
                job_exists = False
                current_crontab = ""
            
            # Check if script exists
            script_exists = self.script_path.exists()
            
            # Check if log file exists and get recent entries
            log_exists = self.log_file.exists()
            recent_logs = []
            
            if log_exists:
                try:
                    with open(self.log_file, 'r') as f:
                        lines = f.readlines()
                        recent_logs = lines[-10:] if len(lines) > 10 else lines
                except Exception as e:
                    logger.warning(f"Could not read log file: {str(e)}")
            
            # Check cron service status (Linux/macOS)
            cron_service_running = self._check_cron_service()
            
            status = {
                'cron_job_installed': job_exists,
                'script_exists': script_exists,
                'log_file_exists': log_exists,
                'cron_service_running': cron_service_running,
                'script_path': str(self.script_path),
                'log_file_path': str(self.log_file),
                'cron_schedule': self.cron_schedule,
                'recent_logs': [line.strip() for line in recent_logs]
            }
            
            # Print status
            print("\n📊 Cron Job Status:")
            print(f"   Cron job installed: {'✅' if job_exists else '❌'}")
            print(f"   Script exists: {'✅' if script_exists else '❌'}")
            print(f"   Log file exists: {'✅' if log_exists else '❌'}")
            print(f"   Cron service running: {'✅' if cron_service_running else '❌'}")
            print(f"   Schedule: {self.cron_schedule} (Every 12 hours)")
            print(f"   Script: {self.script_path}")
            print(f"   Logs: {self.log_file}")
            
            if recent_logs:
                print(f"\n📝 Recent log entries ({len(recent_logs)} lines):")
                for log_line in recent_logs[-5:]:  # Show last 5 lines
                    print(f"   {log_line}")
            
            return status
            
        except Exception as e:
            logger.error(f"Error checking cron status: {str(e)}")
            return {'error': str(e)}
    
    def _check_cron_service(self) -> bool:
        """Check if cron service is running"""
        try:
            # Try different methods to check cron service
            commands = [
                ['pgrep', 'cron'],
                ['pgrep', 'crond'],
                ['systemctl', 'is-active', 'cron'],
                ['systemctl', 'is-active', 'crond'],
                ['service', 'cron', 'status']
            ]
            
            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, timeout=5)
                    if result.returncode == 0:
                        return True
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    continue
            
            return False
            
        except Exception:
            return False
    
    def test_sync_script(self) -> bool:
        """Test the sync script"""
        try:
            logger.info("Testing auto-sync script...")
            
            if not self.script_path.exists():
                logger.error(f"Script not found: {self.script_path}")
                return False
            
            # Run the script
            logger.info(f"Running: python {self.script_path}")
            result = subprocess.run([sys.executable, str(self.script_path)], 
                                  capture_output=True, text=True, timeout=300)
            
            print(f"\n📋 Test Results:")
            print(f"   Exit code: {result.returncode}")
            print(f"   Duration: ~5 minutes (estimated)")
            
            if result.stdout:
                print(f"\n📤 Output:")
                print(result.stdout)
            
            if result.stderr:
                print(f"\n📥 Errors:")
                print(result.stderr)
            
            if result.returncode == 0:
                logger.info("✅ Test completed successfully!")
                return True
            else:
                logger.error("❌ Test failed!")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ Test timed out (>5 minutes)")
            return False
        except Exception as e:
            logger.error(f"Error testing script: {str(e)}")
            return False


def main():
    """Main entry point"""
    if len(sys.argv) != 2:
        print("Usage: python setup_cron.py [install|remove|status|test]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    cron_manager = CronManager()
    
    if command == 'install':
        success = cron_manager.install_cron_job()
        sys.exit(0 if success else 1)
        
    elif command == 'remove':
        success = cron_manager.remove_cron_job()
        sys.exit(0 if success else 1)
        
    elif command == 'status':
        cron_manager.check_cron_status()
        sys.exit(0)
        
    elif command == 'test':
        success = cron_manager.test_sync_script()
        sys.exit(0 if success else 1)
        
    else:
        print(f"Unknown command: {command}")
        print("Available commands: install, remove, status, test")
        sys.exit(1)


if __name__ == "__main__":
    main()
