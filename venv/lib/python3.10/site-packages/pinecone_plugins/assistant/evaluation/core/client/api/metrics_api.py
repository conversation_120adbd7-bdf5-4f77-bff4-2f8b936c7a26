"""
    Evaluation API

    Provides endpoints for evaluating RAG systems using various metrics.  # noqa: E501

    The version of the OpenAPI document: 2025-04
    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401
import sys  # noqa: F401

from pinecone_plugins.assistant.evaluation.core.client.api_client import ApiClient, Endpoint as _Endpoint
from pinecone_plugins.assistant.evaluation.core.client.model_utils import (  # noqa: F401
    check_allowed_values,
    check_validations,
    date,
    datetime,
    file_type,
    none_type,
    validate_and_convert_types
)
from pinecone_plugins.assistant.evaluation.core.client.model.alignment_request import AlignmentRequest
from pinecone_plugins.assistant.evaluation.core.client.model.alignment_response import AlignmentResponse
from pinecone_plugins.assistant.evaluation.core.client.model.basic_error_response import BasicErrorResponse


class MetricsApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

        def __metrics_alignment(
            self,
            alignment_request,
            **kwargs
        ):
            """Evaluate an answer  # noqa: E501

            Evaluate the correctness and completeness of a response from an assistant or a RAG system. The correctness and completeness are evaluated based on the precision and recall of the generated answer with respect to the ground truth answer facts. Alignment is the harmonic mean of correctness and completeness.  For guidance and examples, see [Evaluate answers](https://docs.pinecone.io/guides/assistant/evaluate-answers).  # noqa: E501
            This method makes a synchronous HTTP request by default. To make an
            asynchronous HTTP request, please pass async_req=True

            >>> thread = api.metrics_alignment(alignment_request, async_req=True)
            >>> result = thread.get()

            Args:
                alignment_request (AlignmentRequest): The request body for the alignment evaluation.

            Keyword Args:
                _return_http_data_only (bool): response data without head status
                    code and headers. Default is True.
                _preload_content (bool): if False, the urllib3.HTTPResponse object
                    will be returned without reading/decoding response data.
                    Default is True.
                _request_timeout (int/float/tuple): timeout setting for this request. If
                    one number provided, it will be total request timeout. It can also
                    be a pair (tuple) of (connection, read) timeouts.
                    Default is None.
                _check_input_type (bool): specifies if type checking
                    should be done one the data sent to the server.
                    Default is True.
                _check_return_type (bool): specifies if type checking
                    should be done one the data received from the server.
                    Default is True.
                _host_index (int/None): specifies the index of the server
                    that we want to use.
                    Default is read from the configuration.
                async_req (bool): execute request asynchronously

            Returns:
                AlignmentResponse
                    If the method is called asynchronously, returns the request
                    thread.
            """
            kwargs['async_req'] = kwargs.get(
                'async_req', False
            )
            kwargs['_return_http_data_only'] = kwargs.get(
                '_return_http_data_only', True
            )
            kwargs['_preload_content'] = kwargs.get(
                '_preload_content', True
            )
            kwargs['_request_timeout'] = kwargs.get(
                '_request_timeout', None
            )
            kwargs['_check_input_type'] = kwargs.get(
                '_check_input_type', True
            )
            kwargs['_check_return_type'] = kwargs.get(
                '_check_return_type', True
            )
            kwargs['_host_index'] = kwargs.get('_host_index')
            kwargs['alignment_request'] = \
                alignment_request
            return self.call_with_http_info(**kwargs)

        self.metrics_alignment = _Endpoint(
            settings={
                'response_type': (AlignmentResponse,),
                'auth': [
                    'ApiKeyAuth'
                ],
                'endpoint_path': '/evaluation/metrics/alignment',
                'operation_id': 'metrics_alignment',
                'http_method': 'POST',
                'servers': None,
            },
            params_map={
                'all': [
                    'alignment_request',
                ],
                'required': [
                    'alignment_request',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'alignment_request':
                        (AlignmentRequest,),
                },
                'attribute_map': {
                },
                'location_map': {
                    'alignment_request': 'body',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json'
                ],
                'content_type': [
                    'application/json'
                ]
            },
            api_client=api_client,
            callable=__metrics_alignment
        )
