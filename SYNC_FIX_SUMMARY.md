# Sync Duplicate Files Fix

## Problem Description

The issue you encountered was related to duplicate file creation during the sync process. Here's what was happening:

1. **Initial Assistant Creation**: When you deleted the <PERSON><PERSON><PERSON> assistant and added a store, a new assistant was created and 3 files were uploaded (products, orders, customers).

2. **First Sync**: When you pressed the sync button, the system:
   - Fetched fresh data from Shopify
   - Calculated new hashes for the data
   - Compared with stored hashes in the database
   - Since the database hashes were being set BEFORE the sync completed successfully, there was a mismatch
   - The system thought the data had changed and uploaded 3 more files, creating duplicates (total of 6 files)

3. **Manual Cleanup**: When you manually deleted the old files and synced again, it worked correctly because the hashes finally matched.

## Root Causes

1. **Hash Timing Issue**: Database hashes were being updated BEFORE the sync operation completed successfully
2. **No Duplicate Detection**: The system didn't check for and clean up existing duplicate files
3. **Null Hash Handling**: Newly created assistants had null hashes, which weren't being handled properly

## Implemented Fixes

### 1. Fixed Hash Synchronization During Assistant Creation

**File**: `pinecone_assistant.py` - `_create_new_assistant()` method

**Changes**:
- Modified the assistant creation process to perform sync FIRST, then update database hashes only if sync was successful
- Added proper error handling and logging
- Ensured database consistency

**Before**:
```python
# Store hashes in database BEFORE sync
AssistantCRUD.update_sync_status(db, assistant_record.id, ...)
# Perform sync
self.selective_sync(...)
```

**After**:
```python
# Perform sync first (don't update DB hashes yet)
sync_result = self.selective_sync(..., update_db_hashes=False)
# Only update database hashes if sync was successful
if sync_result and sync_result.get("success"):
    AssistantCRUD.update_sync_status(db, assistant_record.id, ...)
```

### 2. Added Duplicate File Cleanup

**File**: `pinecone_assistant.py` - `_cleanup_duplicate_files()` method

**Changes**:
- Added a new method to detect and remove duplicate files
- Groups files by data type and chunk number
- Keeps only the newest file and deletes older duplicates
- Called automatically at the beginning of each sync operation

**Features**:
- Handles both single files and chunked files
- Sorts by creation time to keep the newest version
- Provides detailed logging of cleanup operations
- Graceful error handling

### 3. Improved Sync Logic for New Assistants

**File**: `assistants/multi_store_assistant.py` - `sync_store_data()` method

**Changes**:
- Modified hash comparison logic to handle null hashes properly
- When hash is None (newly created assistant), it's considered as changed and will sync
- Added better logging to distinguish between new assistants and actual data changes

**Before**:
```python
if assistant_record.products_hash != products_hash:
    sync_products = True
```

**After**:
```python
if assistant_record.products_hash is None or assistant_record.products_hash != products_hash:
    sync_products = True
    if assistant_record.products_hash is None:
        logger.info("Products hash is None (new assistant), will sync")
```

### 4. Enhanced Selective Sync Method

**File**: `pinecone_assistant.py` - `selective_sync()` method

**Changes**:
- Added `update_db_hashes` parameter to control when database hashes are updated
- During initial creation, hashes are not updated until after successful sync
- Added duplicate cleanup at the beginning of sync operations
- Improved error handling and logging

## How the Fix Resolves the Issue

1. **Prevents Initial Duplicates**: By updating database hashes only after successful sync, the system won't think data has changed on the first sync after creation.

2. **Cleans Up Existing Duplicates**: The cleanup method automatically removes any duplicate files that might exist from previous failed operations.

3. **Handles New Assistants Properly**: The sync logic now correctly identifies when an assistant is newly created and handles it appropriately.

4. **Maintains Data Integrity**: All changes include proper error handling and logging to ensure data consistency.

## Testing

A test script (`test_sync_fix.py`) has been created to verify the fix:

1. Deletes existing assistant
2. Creates new assistant (simulates adding store)
3. Performs sync operation
4. Verifies no duplicate files are created
5. Performs second sync to ensure stability

## Expected Behavior After Fix

1. **Assistant Creation**: Creates assistant with 3 files, database hashes are set correctly
2. **First Sync**: Detects no changes (hashes match), skips sync with message "No changes detected, sync skipped"
3. **Subsequent Syncs**: Only sync when actual data changes occur
4. **No Duplicates**: Automatic cleanup prevents duplicate files from accumulating

## Verification Steps

To verify the fix is working:

1. Delete a Pinecone assistant
2. Add the store again (creates new assistant with 3 files)
3. Press sync button immediately
4. Should see message: "No changes detected, sync skipped"
5. File count should remain at 3 (no duplicates)

The fix ensures that the sync process is now robust, efficient, and prevents the duplicate file issue you experienced.
