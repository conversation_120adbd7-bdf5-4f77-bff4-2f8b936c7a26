#!/usr/bin/env python3
"""
Cron-Based Auto-Sync Script for Store Data Synchronization

This standalone script runs independently of the FastAPI application
and can be executed via system cron jobs every 12 hours at 12:00 AM and 12:00 PM UTC.

Usage:
    python auto_sync_cron.py

Cron Schedule (add to crontab):
    0 0,12 * * * cd /path/to/your/app && python auto_sync_cron.py >> /var/log/auto_sync.log 2>&1
"""

import os
import sys
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import database and models
from sqlalchemy.orm import Session
from database.session import get_db
from database.models import Store
from assistants.multi_store_assistant import multi_store_manager

# Initialize logging for cron
from logging_config import init_logging
init_logging()
logger = logging.getLogger('cron_sync')

class CronAutoSync:
    """
    Standalone cron-based auto-sync system for store data synchronization.
    
    This class handles automatic synchronization of all active stores
    independently of the FastAPI application.
    """
    
    def __init__(self):
        """Initialize the cron auto-sync system"""
        self.sync_start_time = None
        self.sync_results = {
            'total': 0,
            'success': 0,
            'skipped': 0,
            'failed': 0,
            'errors': []
        }
        
    def run_sync(self) -> Dict[str, Any]:
        """
        Main sync execution method
        
        Returns:
            Dict containing sync results and statistics
        """
        self.sync_start_time = datetime.now(timezone.utc)
        logger.info(f"🔄 Starting cron-based auto-sync at {self.sync_start_time}")
        
        try:
            # Get database session
            db = next(get_db())
            
            try:
                # Get all active stores
                stores = self._get_all_active_stores(db)
                
                if not stores:
                    logger.info("No active stores found for auto-sync")
                    return self._create_result_summary(0)
                
                logger.info(f"Found {len(stores)} active stores for auto-sync")
                self.sync_results['total'] = len(stores)
                
                # Sync each store
                for store in stores:
                    try:
                        result = self._sync_single_store(db, store)
                        self._update_sync_results(store, result)
                        
                    except Exception as e:
                        self._handle_store_error(store, e)
                
                # Log final results
                return self._create_result_summary(len(stores))
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ Critical error during cron auto-sync: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'sync_duration': 0
            }
    
    def _get_all_active_stores(self, db: Session) -> List[Store]:
        """Get all active stores from the database"""
        try:
            stores = db.query(Store).filter(
                Store.status == 'active',
                Store.is_deleted == False
            ).all()
            
            return stores
            
        except Exception as e:
            logger.error(f"Error fetching active stores: {str(e)}")
            return []
    
    def _sync_single_store(self, db: Session, store: Store) -> Dict[str, Any]:
        """
        Sync a single store and return result summary
        
        Returns:
            Dict with keys: success (bool), skipped (bool), message (str)
        """
        try:
            logger.info(f"Syncing store {store.id} ({store.shop_url})")
            
            # Use the multi_store_manager to sync with change detection
            result = multi_store_manager.sync_store_data(
                db=db,
                store_id=store.id,
                user_id=store.user_id,
                force=False  # Use change detection
            )
            
            return {
                'success': result.success,
                'skipped': 'skipped' in result.message.lower(),
                'message': result.message,
                'duration': getattr(result, 'sync_duration', 0)
            }
            
        except Exception as e:
            logger.error(f"Error syncing store {store.id}: {str(e)}")
            return {
                'success': False,
                'skipped': False,
                'message': f"Sync failed: {str(e)}",
                'duration': 0
            }
    
    def _update_sync_results(self, store: Store, result: Dict[str, Any]):
        """Update sync results based on individual store sync result"""
        if result['success']:
            if result['skipped']:
                self.sync_results['skipped'] += 1
                logger.info(f"✅ Store {store.id} ({store.shop_url}): {result['message']}")
            else:
                self.sync_results['success'] += 1
                logger.info(f"✅ Store {store.id} ({store.shop_url}): Synced successfully")
        else:
            self.sync_results['failed'] += 1
            error_msg = f"Store {store.id}: {result['message']}"
            self.sync_results['errors'].append(error_msg)
            logger.error(f"❌ Store {store.id} ({store.shop_url}): {result['message']}")
    
    def _handle_store_error(self, store: Store, error: Exception):
        """Handle unexpected errors during store sync"""
        self.sync_results['failed'] += 1
        error_msg = f"Store {store.id}: Unexpected error - {str(error)}"
        self.sync_results['errors'].append(error_msg)
        logger.error(f"❌ {error_msg}")
    
    def _create_result_summary(self, total_stores: int) -> Dict[str, Any]:
        """Create final sync result summary"""
        sync_duration = (datetime.now(timezone.utc) - self.sync_start_time).total_seconds()
        
        # Log summary
        self._log_sync_summary(sync_duration)
        
        # Save sync status to file for API access
        self._save_sync_status(sync_duration)
        
        return {
            'success': True,
            'total_stores': total_stores,
            'results': self.sync_results,
            'sync_duration': sync_duration,
            'sync_time': self.sync_start_time.isoformat()
        }
    
    def _log_sync_summary(self, duration: float):
        """Log a summary of the sync operation"""
        logger.info("🔄 Cron auto-sync completed!")
        logger.info(f"📊 Results: {self.sync_results['success']} synced, "
                   f"{self.sync_results['skipped']} skipped, "
                   f"{self.sync_results['failed']} failed out of {self.sync_results['total']} stores")
        logger.info(f"⏱️  Duration: {duration:.2f} seconds")
        
        if self.sync_results['errors']:
            logger.warning(f"❌ Errors encountered:")
            for error in self.sync_results['errors']:
                logger.warning(f"   - {error}")
    
    def _save_sync_status(self, duration: float):
        """Save sync status to file for API access"""
        try:
            status_data = {
                'last_sync_time': self.sync_start_time.isoformat(),
                'sync_duration': duration,
                'results': self.sync_results,
                'next_sync_times': [
                    '00:00:00 UTC (Daily)',
                    '12:00:00 UTC (Daily)'
                ],
                'sync_type': 'cron_based',
                'status': 'completed'
            }
            
            # Save to status file
            import json
            status_file = project_root / 'logs' / 'sync' / 'cron_status.json'
            status_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(status_file, 'w') as f:
                json.dump(status_data, f, indent=2)
                
            logger.info(f"Sync status saved to {status_file}")
            
        except Exception as e:
            logger.error(f"Failed to save sync status: {str(e)}")


def main():
    """Main entry point for cron execution"""
    try:
        # Create and run sync
        cron_sync = CronAutoSync()
        result = cron_sync.run_sync()
        
        if result['success']:
            print(f"✅ Cron sync completed successfully in {result['sync_duration']:.2f}s")
            print(f"📊 Results: {result['results']['success']} synced, "
                  f"{result['results']['skipped']} skipped, "
                  f"{result['results']['failed']} failed")
            sys.exit(0)
        else:
            print(f"❌ Cron sync failed: {result.get('error', 'Unknown error')}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Critical error in cron sync: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
