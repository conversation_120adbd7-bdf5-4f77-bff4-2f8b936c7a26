import os
import requests
import urllib3
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Suppress SSL verification warnings if verify=False is used
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ShopifyGraphQLClient:
    def __init__(self, shop_url=None, access_token=None):
        """
        Initialize Shopify GraphQL client with dynamic credentials.

        Args:
            shop_url: Shopify store URL (e.g., "mystore.myshopify.com")
            access_token: Shopify access token

        If credentials are not provided, will attempt to load from environment variables
        (for backward compatibility during transition)
        """
        self.shop_url = shop_url or os.getenv("SHOPIFY_SHOP_URL")
        self.access_token = access_token or os.getenv("SHOPIFY_ACCESS_TOKEN")

        if not all([self.shop_url, self.access_token]):
            raise ValueError("Missing Shopify API credentials. Provide shop_url and access_token parameters or set environment variables.")

        # Get API version from environment or use default
        api_version = os.getenv("SHOPIFY_API_VERSION", "2025-01")
        self.url = f"https://{self.shop_url}/admin/api/{api_version}/graphql.json"
        self.headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": self.access_token
        }

    def execute(self, query, variables=None):
        """Execute a GraphQL query against the Shopify Admin API."""
        payload = {
            "query": query,
            "variables": variables or {}
        }

        response = requests.post(
            self.url,
            headers=self.headers,
            json=payload,
            verify=False
        )

        if response.status_code != 200:
            raise Exception(f"Query failed with status code {response.status_code}: {response.text}")

        result = response.json()

        if "errors" in result:
            raise Exception(f"GraphQL query errors: {result['errors']}")

        return result["data"]

def fetch_products(client):
    """
    Fetch all products from Shopify with all required fields.

    This query ensures we get all the fields needed as specified in the plan.md file:
    - id, title, body_html, vendor, product_type, created_at, updated_at, published_at
    - tags, status
    - variants with all their details (id, price, inventory, etc.)
    - options
    - images
    """
    query = """
    query fetchProducts($cursor: String) {
      products(first: 50, after: $cursor) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            legacyResourceId
            title
            descriptionHtml
            vendor
            productType
            createdAt
            updatedAt
            publishedAt
            tags
            status
            options {
              id
              name
              position
              values
            }
            variants(first: 50) {
              edges {
                node {
                  id
                  title
                  price
                  compareAtPrice
                  inventoryPolicy
                  inventoryQuantity
                  sku
                  taxable
                  position
                  barcode
                  selectedOptions {
                    name
                    value
                  }
                }
              }
            }
            images(first: 20) {
              edges {
                node {
                  id
                  altText
                  originalSrc
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
    """

    products = []
    has_next_page = True
    cursor = None

    while has_next_page:
        variables = {"cursor": cursor} if cursor else {}
        result = client.execute(query, variables)

        page_info = result["products"]["pageInfo"]
        has_next_page = page_info["hasNextPage"]
        cursor = page_info["endCursor"] if has_next_page else None

        for edge in result["products"]["edges"]:
            product = edge["node"]

            # Convert GraphQL format to match the expected JSON structure
            formatted_product = {
                "id": int(product["legacyResourceId"]),
                "title": product["title"],
                "body_html": product["descriptionHtml"],
                "vendor": product["vendor"],
                "product_type": product["productType"],
                "created_at": product["createdAt"],
                "updated_at": product["updatedAt"],
                "published_at": product["publishedAt"],
                "tags": ", ".join(product["tags"]) if isinstance(product["tags"], list) else product["tags"],
                "status": product["status"].lower(),
                "options": [
                    {
                        "id": int(option["id"].split("/")[-1]),
                        "product_id": int(product["legacyResourceId"]),
                        "name": option["name"],
                        "position": option["position"],
                        "values": option["values"]
                    }
                    for option in product["options"]
                ],
                "variants": [
                    {
                        "id": int(variant["node"]["id"].split("/")[-1]),
                        "product_id": int(product["legacyResourceId"]),
                        "title": variant["node"]["title"],
                        "price": variant["node"]["price"],
                        "inventory_policy": variant["node"]["inventoryPolicy"].lower(),
                        "inventory_quantity": variant["node"]["inventoryQuantity"],
                        "inventory_management": "shopify",  # Default value
                        "sku": variant["node"]["sku"],
                        "taxable": variant["node"]["taxable"],
                        "option1": next((opt["value"] for opt in variant["node"]["selectedOptions"] if product["options"] and opt["name"] == product["options"][0]["name"]), None) if product["options"] else None,
                        "option2": next((opt["value"] for opt in variant["node"]["selectedOptions"] if len(product["options"]) > 1 and opt["name"] == product["options"][1]["name"]), None) if len(product["options"]) > 1 else None,
                        "option3": next((opt["value"] for opt in variant["node"]["selectedOptions"] if len(product["options"]) > 2 and opt["name"] == product["options"][2]["name"]), None) if len(product["options"]) > 2 else None,
                    }
                    for variant in product["variants"]["edges"]
                ],
                "images": [
                    {
                        "id": int(image["node"]["id"].split("/")[-1]),
                        "product_id": int(product["legacyResourceId"]),
                        "alt": image["node"]["altText"] or "",
                        "position": idx + 1,
                        "src": image["node"]["originalSrc"],
                        "width": image["node"]["width"],
                        "height": image["node"]["height"]
                    }
                    for idx, image in enumerate(product["images"]["edges"])
                ]
            }

            products.append(formatted_product)

    return {"products": products}

def fetch_orders(client):
    """
    Fetch all orders from Shopify with all required fields.

    This query ensures we get all the fields needed as specified in the plan.md file:
    - id, name, order_number, email, phone, financial_status, fulfillment_status
    - created_at, processed_at, currency
    - subtotal_price, total_discounts, total_line_items_price, total_price, total_tax
    - billing_address, shipping_address
    - customer details
    - line_items with all their details
    """
    query = """
    query fetchOrders($cursor: String) {
      orders(first: 50, after: $cursor) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            legacyResourceId
            name
            email
            phone
            confirmed
            createdAt
            processedAt
            currencyCode
            cancelReason
            cancelledAt
            closed
            closedAt
            confirmationNumber
            displayFinancialStatus
            displayFulfillmentStatus

            currentSubtotalPriceSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }
            currentTotalPriceSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }
            currentTotalTaxSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }

            subtotalPriceSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }
            totalDiscountsSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }
            totalPriceSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }
            totalShippingPriceSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }
            totalTaxSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }

            totalRefunded
            totalRefundedShippingSet {
              shopMoney { amount currencyCode }
              presentmentMoney { amount currencyCode }
            }

            taxesIncluded
            taxExempt

            customer {
              id
              legacyResourceId
              firstName
              lastName
              email
              phone
              verifiedEmail
              taxExempt
              tags
              defaultAddress {
                address1
                address2
                city
                company
                country
                countryCode
                firstName
                lastName
                phone
                province
                provinceCode
                zip
              }
            }

            shippingAddress {
              address1
              address2
              city
              company
              country
              countryCode
              firstName
              lastName
              phone
              province
              provinceCode
              zip
            }

            billingAddress {
              address1
              address2
              city
              company
              country
              countryCode
              firstName
              lastName
              phone
              province
              provinceCode
              zip
            }

            lineItems(first: 50) {
              edges {
                node {
                  id
                  name
                  quantity
                  title
                  variantTitle
                  vendor
                  sku
                  originalTotalSet {
                    shopMoney { amount currencyCode }
                    presentmentMoney { amount currencyCode }
                  }
                  originalUnitPriceSet {
                    shopMoney { amount currencyCode }
                    presentmentMoney { amount currencyCode }
                  }
                  discountedTotalSet {
                    shopMoney { amount currencyCode }
                    presentmentMoney { amount currencyCode }
                  }
                  discountedUnitPriceSet {
                    shopMoney { amount currencyCode }
                    presentmentMoney { amount currencyCode }
                  }
                  taxable
                  taxLines {
                    priceSet {
                      shopMoney { amount currencyCode }
                      presentmentMoney { amount currencyCode }
                    }
                    rate
                    title
                  }
                }
              }
            }
          }
        }
      }
    }
    """

    orders = []
    has_next_page = True
    cursor = None

    while has_next_page:
        variables = {"cursor": cursor} if cursor else {}
        result = client.execute(query, variables)

        page_info = result["orders"]["pageInfo"]
        has_next_page = page_info["hasNextPage"]
        cursor = page_info["endCursor"] if has_next_page else None

        for edge in result["orders"]["edges"]:
            order = edge["node"]

            # Convert GraphQL format to match the expected JSON structure
            formatted_order = {
                "id": int(order["legacyResourceId"]),
                "confirmation_number": order["confirmationNumber"],
                "confirmed": order["confirmed"],
                "created_at": order["createdAt"],
                "currency": order["currencyCode"],
                "current_subtotal_price": order["currentSubtotalPriceSet"]["shopMoney"]["amount"],
                "current_total_price": order["currentTotalPriceSet"]["shopMoney"]["amount"],
                "current_total_tax": order["currentTotalTaxSet"]["shopMoney"]["amount"],
                "email": order["email"],
                "name": order["name"],
                "order_number": int(order["name"].replace("#", "")),
                "phone": order["phone"],
                "processed_at": order["processedAt"],
                "financial_status": order["displayFinancialStatus"].lower() if order["displayFinancialStatus"] else None,
                "fulfillment_status": order["displayFulfillmentStatus"].lower() if order["displayFulfillmentStatus"] else None,
                "subtotal_price": order["subtotalPriceSet"]["shopMoney"]["amount"],
                "total_discounts": order["totalDiscountsSet"]["shopMoney"]["amount"],
                "total_price": order["totalPriceSet"]["shopMoney"]["amount"],
                "total_tax": order["totalTaxSet"]["shopMoney"]["amount"],
                "taxes_included": order["taxesIncluded"],
                "tax_exempt": order["taxExempt"],

                "billing_address": {
                    "first_name": order["billingAddress"]["firstName"] if order["billingAddress"] else None,
                    "last_name": order["billingAddress"]["lastName"] if order["billingAddress"] else None,
                    "address1": order["billingAddress"]["address1"] if order["billingAddress"] else None,
                    "address2": order["billingAddress"]["address2"] if order["billingAddress"] else None,
                    "city": order["billingAddress"]["city"] if order["billingAddress"] else None,
                    "province": order["billingAddress"]["province"] if order["billingAddress"] else None,
                    "country": order["billingAddress"]["country"] if order["billingAddress"] else None,
                    "zip": order["billingAddress"]["zip"] if order["billingAddress"] else None,
                    "phone": order["billingAddress"]["phone"] if order["billingAddress"] else None,
                } if order["billingAddress"] else None,

                "shipping_address": {
                    "first_name": order["shippingAddress"]["firstName"] if order["shippingAddress"] else None,
                    "last_name": order["shippingAddress"]["lastName"] if order["shippingAddress"] else None,
                    "address1": order["shippingAddress"]["address1"] if order["shippingAddress"] else None,
                    "address2": order["shippingAddress"]["address2"] if order["shippingAddress"] else None,
                    "city": order["shippingAddress"]["city"] if order["shippingAddress"] else None,
                    "province": order["shippingAddress"]["province"] if order["shippingAddress"] else None,
                    "country": order["shippingAddress"]["country"] if order["shippingAddress"] else None,
                    "zip": order["shippingAddress"]["zip"] if order["shippingAddress"] else None,
                    "phone": order["shippingAddress"]["phone"] if order["shippingAddress"] else None,
                } if order["shippingAddress"] else None,

                "customer": {
                    "id": int(order["customer"]["legacyResourceId"]) if order["customer"] else None,
                    "email": order["customer"]["email"] if order["customer"] else None,
                    "first_name": order["customer"]["firstName"] if order["customer"] else None,
                    "last_name": order["customer"]["lastName"] if order["customer"] else None,
                    "phone": order["customer"]["phone"] if order["customer"] else None
                } if order["customer"] else None,

                "line_items": [
                    {
                        "id": int(item["node"]["id"].split("/")[-1]),
                        "name": item["node"]["name"],
                        "price": item["node"]["originalUnitPriceSet"]["shopMoney"]["amount"],
                        "quantity": item["node"]["quantity"],
                        "sku": item["node"]["sku"],
                        "title": item["node"]["title"],
                        "variant_title": item["node"]["variantTitle"],
                        "vendor": item["node"]["vendor"],
                    }
                    for item in order["lineItems"]["edges"]
                ]
            }

            orders.append(formatted_order)

    return {"orders": orders}

def fetch_customers(client):
    """
    Fetch all customers from Shopify with all required fields.

    This query ensures we get all the fields needed as specified in the plan.md file:
    - id, email, first_name, last_name, created_at, updated_at
    - orders_count, state, total_spent, last_order_id, verified_email
    - currency, phone
    - addresses with all their details
    - email_marketing_consent, sms_marketing_consent
    """
    query = """
    query fetchCustomers($cursor: String) {
      customers(first: 50, after: $cursor) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            legacyResourceId
            firstName
            lastName
            createdAt
            updatedAt
            verifiedEmail
            defaultAddress {
              id
              address1
              address2
              city
              company
              country
              countryCode
              firstName
              lastName
              phone
              province
              provinceCode
              zip
            }
            addresses(first: 10) {
              id
              address1
              address2
              city
              company
              country
              countryCode
              firstName
              lastName
              phone
              province
              provinceCode
              zip
            }
            phone
            email
            taxExempt
            tags
            numberOfOrders
            amountSpent {
              amount
              currencyCode
            }
          }
        }
      }
    }
    """

    customers = []
    has_next_page = True
    cursor = None

    while has_next_page:
        variables = {"cursor": cursor} if cursor else {}
        result = client.execute(query, variables)

        page_info = result["customers"]["pageInfo"]
        has_next_page = page_info["hasNextPage"]
        cursor = page_info["endCursor"] if has_next_page else None

        for edge in result["customers"]["edges"]:
            customer = edge["node"]

            # Convert GraphQL format to match the expected JSON structure
            formatted_customer = {
                "id": int(customer["legacyResourceId"]),
                "email": customer["email"],
                "created_at": customer["createdAt"],
                "updated_at": customer["updatedAt"],
                "first_name": customer["firstName"],
                "last_name": customer["lastName"],
                "orders_count": customer["numberOfOrders"],
                "state": "enabled",  # Default value
                "total_spent": customer["amountSpent"]["amount"] if customer["amountSpent"] else "0.00",
                "verified_email": customer["verifiedEmail"],
                "phone": customer["phone"],
                "tax_exempt": customer["taxExempt"],
                "tags": customer["tags"],

                "addresses": [
                    {
                        "id": int(address["id"].split("/")[-1].split("?")[0]),
                        "customer_id": int(customer["legacyResourceId"]),
                        "first_name": address["firstName"],
                        "last_name": address["lastName"],
                        "company": address["company"],
                        "address1": address["address1"],
                        "address2": address["address2"],
                        "city": address["city"],
                        "province": address["province"],
                        "country": address["country"],
                        "zip": address["zip"],
                        "phone": address["phone"],
                        "default": address["id"] == customer["defaultAddress"]["id"] if customer["defaultAddress"] else False
                    }
                    for address in customer["addresses"]
                ],

                "email_marketing_consent": {
                    "state": "not_subscribed",  # Default value since we can't get marketing state
                    "opt_in_level": "single_opt_in",  # Default value
                    "consent_updated_at": customer["updatedAt"]  # Use customer updated_at as fallback
                } if customer["email"] else None
            }

            customers.append(formatted_customer)

    return {"customers": customers}