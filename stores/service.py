from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from database.models import Store
from database.crud import StoreCRUD, AssistantCRUD
from .shopify_client import ShopifyAPIClient, generate_store_id, extract_shop_name
from .models import StoreUpdate, StoreStats, StoreManualCreate, StoreConnectionResult

class StoreService:
    """Service class for store management operations"""

    def __init__(self):
        pass

    def create_store_from_manual(self, db: Session, user_id: int, shop_url: str, access_token: str) -> Store:
        """Create a new store connection from manual input"""
        # Get shop information using GraphQL
        shopify_client = ShopifyAPIClient(shop_url, access_token)
        shop_info = shopify_client.get_shop_info_graphql()

        if not shop_info or 'shop' not in shop_info:
            raise ValueError("Failed to retrieve shop information from GraphQL")

        shop_data = shop_info['shop']

        # Extract Shopify store ID from GraphQL format: "gid://shopify/Shop/123456"
        shopify_store_id = shop_data['id'].split('/')[-1] if shop_data.get('id') else generate_store_id(shop_url)

        # Check if this user already has this store connected
        existing_user_store = StoreCRUD.get_store_by_user_and_shopify_id(db, user_id, shopify_store_id)
        if existing_user_store:
            raise ValueError("You have already connected this store to your account")

        # Extract data from GraphQL response
        store_name = shop_data.get('name') or extract_shop_name(shop_url)
        email = shop_data.get('email')

        # Domain handling for GraphQL
        domain = shop_data.get('primaryDomain', {}).get('host') if shop_data.get('primaryDomain') else None
        myshopify_domain = shop_data.get('myshopifyDomain')

        # Create store record
        store = StoreCRUD.create_store(
            db=db,
            user_id=user_id,
            shopify_store_id=shopify_store_id,
            shop_url=shop_url,
            access_token=access_token,
            store_name=store_name,
            domain=domain,
            myshopify_domain=myshopify_domain,
            email=email
        )

        # Automatically create assistant for the new store (data upload happens during creation)
        try:
            from assistants.multi_store_assistant import multi_store_manager
            assistant = multi_store_manager.create_assistant_for_store(db, store)

            if assistant:
                print(f"Assistant created successfully for store {store.id}")
            else:
                print(f"Warning: Failed to create assistant for store {store.id}")
        except Exception as e:
            # Log the error but don't fail store creation
            print(f"Warning: Failed to create assistant for store {store.id}: {str(e)}")

        return store

    def create_store_manual(self, db: Session, user_id: int, store_data: StoreManualCreate) -> Store:
        """Create a new store connection using manual method (shop URL + access token)"""
        return self.create_store_from_manual(db, user_id, store_data.shop_url, store_data.access_token)

    def test_store_connection(self, shop_url: str, access_token: str) -> StoreConnectionResult:
        """Test connection to a Shopify store"""
        try:
            # Validate inputs
            if not shop_url or not shop_url.strip():
                return StoreConnectionResult(
                    success=False,
                    message="Shop URL is required",
                    error_details="Empty shop URL provided"
                )

            if not access_token or not access_token.strip():
                return StoreConnectionResult(
                    success=False,
                    message="Access token is required",
                    error_details="Empty access token provided"
                )

            # Validate access token format
            access_token = access_token.strip()
            if not access_token.startswith('shpat_'):
                return StoreConnectionResult(
                    success=False,
                    message="Invalid access token format",
                    error_details="Private app access token should start with 'shpat_'"
                )

            # Validate shop URL format
            from .shopify_client import validate_shopify_domain
            try:
                validated_url = validate_shopify_domain(shop_url.strip())
            except ValueError as e:
                return StoreConnectionResult(
                    success=False,
                    message="Invalid shop URL format",
                    error_details=str(e)
                )

            # Test connection
            client = ShopifyAPIClient(validated_url, access_token)
            result = client.test_connection()

            if result['success']:
                return StoreConnectionResult(
                    success=True,
                    message="Connection successful",
                    store_info=result.get('shop_data', {})
                )
            else:
                return StoreConnectionResult(
                    success=False,
                    message=result.get('message', 'Connection failed'),
                    error_details=result.get('error')
                )

        except Exception as e:
            return StoreConnectionResult(
                success=False,
                message="Connection test failed",
                error_details=str(e)
            )

    def get_user_stores(self, db: Session, user_id: int, include_stats: bool = False) -> List[Store]:
        """Get all stores for a user"""
        stores = StoreCRUD.get_stores_by_user(db, user_id)

        if include_stats:
            for store in stores:
                store.stats = self.get_store_stats(store)
                # Get assistant info
                assistant = AssistantCRUD.get_assistant_by_store(db, store.id)
                if assistant:
                    store.assistant_status = assistant.status
                    store.assistant_name = assistant.assistant_name

        return stores

    def get_store_by_id(self, db: Session, store_id: int, user_id: int) -> Optional[Store]:
        """Get store by ID (ensuring user ownership)"""
        store = StoreCRUD.get_store_by_id(db, store_id)
        if store and store.user_id == user_id:
            return store
        return None

    def update_store(self, db: Session, store_id: int, user_id: int, update_data: StoreUpdate) -> Optional[Store]:
        """Update store information"""
        store = self.get_store_by_id(db, store_id, user_id)
        if not store:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)
        return StoreCRUD.update_store(db, store_id, **update_dict)

    def delete_store(self, db: Session, store_id: int, user_id: int, soft_delete: bool = True) -> bool:
        """
        Delete store with shared Pinecone assistant logic.

        - Always deletes the user's store record
        - Only deletes Pinecone assistant if no other users have the same store
        """
        store = self.get_store_by_id(db, store_id, user_id)
        if not store:
            return False

        # Get store details before deletion
        shopify_store_id = store.shopify_store_id

        # Check if other users have the same Shopify store
        has_other_users = StoreCRUD.check_if_store_has_other_users(
            db, shopify_store_id, exclude_user_id=user_id
        )

        # Get assistant before store deletion
        assistant = AssistantCRUD.get_assistant_by_store(db, store_id)

        # Always delete the user's store record first
        store_deleted = StoreCRUD.delete_store(db, store_id, soft_delete)
        if not store_deleted:
            return False

        # Handle assistant deletion based on shared usage
        if assistant:
            if has_other_users:
                # Other users have this store - only delete the database assistant record
                # but keep the Pinecone assistant instance
                print(f"Store {shopify_store_id} has other users - keeping Pinecone assistant, deleting DB record only")
                db.delete(assistant)
                db.commit()

                # Remove from cache if present
                from assistants.multi_store_assistant import multi_store_manager
                if store_id in multi_store_manager.active_assistants:
                    del multi_store_manager.active_assistants[store_id]
                if store_id in multi_store_manager.assistant_cache_timestamps:
                    del multi_store_manager.assistant_cache_timestamps[store_id]
            else:
                # No other users have this store - delete both DB record and Pinecone assistant
                print(f"Store {shopify_store_id} has no other users - deleting Pinecone assistant completely")
                try:
                    # Delete the Pinecone assistant instance
                    from pinecone_assistant import PineconeAssistant
                    pinecone_assistant = PineconeAssistant(assistant_name=assistant.assistant_name)
                    pinecone_assistant.delete_assistant()
                    print(f"Successfully deleted Pinecone assistant: {assistant.assistant_name}")
                except Exception as e:
                    print(f"Warning: Failed to delete Pinecone assistant {assistant.assistant_name}: {e}")

                # Delete the database record
                db.delete(assistant)
                db.commit()

                # Remove from cache if present
                from assistants.multi_store_assistant import multi_store_manager
                if store_id in multi_store_manager.active_assistants:
                    del multi_store_manager.active_assistants[store_id]
                if store_id in multi_store_manager.assistant_cache_timestamps:
                    del multi_store_manager.assistant_cache_timestamps[store_id]

        return True

    def sync_store_data(self, db: Session, store_id: int, user_id: int, force: bool = False) -> bool:
        """Sync store data from Shopify and update Pinecone assistant"""
        store = self.get_store_by_id(db, store_id, user_id)
        if not store:
            return False

        try:
            print(f"DEBUG: Starting sync for store {store_id}...")

            # Decode access token
            access_token = StoreCRUD.decode_token(store.access_token_encrypted)
            client = ShopifyAPIClient(store.shop_url, access_token)

            # Get updated shop info using GraphQL
            shop_info = client.get_shop_info_graphql()
            if 'shop' not in shop_info:
                print("ERROR: Failed to get shop info from GraphQL")
                return False

            shop_data = shop_info['shop']

            # Update store information using GraphQL field names
            update_data = {
                'store_name': shop_data.get('name'),
                'email': shop_data.get('email'),
                'last_sync': datetime.now(timezone.utc)
            }

            StoreCRUD.update_store(db, store_id, **update_data)
            print("DEBUG: Store information updated successfully")

            # Now sync the data to Pinecone assistant
            print("DEBUG: Starting Pinecone assistant data sync...")
            from assistants.multi_store_assistant import multi_store_manager
            sync_result = multi_store_manager.sync_store_data(db, store_id, user_id, force=force)

            if sync_result.success:
                print("DEBUG: Pinecone assistant data sync completed successfully")
                return True
            else:
                print(f"ERROR: Pinecone assistant data sync failed: {sync_result.message}")
                return False

        except Exception as e:
            print(f"ERROR: Store sync failed: {str(e)}")
            import traceback
            traceback.print_exc()
            # Update store status to error
            StoreCRUD.update_store(db, store_id, status='error', last_sync=datetime.now(timezone.utc))
            return False

    def get_store_stats(self, store: Store) -> StoreStats:
        """Get store statistics from Shopify"""
        try:
            access_token = StoreCRUD.decode_token(store.access_token_encrypted)
            client = ShopifyAPIClient(store.shop_url, access_token)

            # Get counts
            products_count = client.get_products_count()
            orders_count = client.get_orders_count()
            customers_count = client.get_customers_count()

            # Get latest order
            latest_order = client.get_latest_order()
            last_order_date = None
            if latest_order:
                last_order_date = datetime.fromisoformat(
                    latest_order['createdAt'].replace('Z', '+00:00')
                )

            return StoreStats(
                total_products=products_count,
                total_orders=orders_count,
                total_customers=customers_count,
                last_order_date=last_order_date,
                store_created_date=store.created_at
            )

        except Exception as e:
            print(f"Error getting store stats: {e}")
            return StoreStats()

    def bulk_operation(self, db: Session, user_id: int, store_ids: List[int], operation: str) -> Dict[str, Any]:
        """Perform bulk operations on stores"""
        results = {
            'success': [],
            'failed': [],
            'total': len(store_ids)
        }

        for store_id in store_ids:
            try:
                if operation == 'sync':
                    success = self.sync_store_data(db, store_id, user_id, force=False)
                elif operation == 'activate':
                    store = self.update_store(db, store_id, user_id, StoreUpdate(status='active'))
                    success = store is not None
                elif operation == 'deactivate':
                    store = self.update_store(db, store_id, user_id, StoreUpdate(status='inactive'))
                    success = store is not None
                elif operation == 'delete':
                    success = self.delete_store(db, store_id, user_id)
                else:
                    success = False

                if success:
                    results['success'].append(store_id)
                else:
                    results['failed'].append(store_id)

            except Exception:
                results['failed'].append(store_id)

        return results
