from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from database.session import get_db
from database.models import User
from auth.dependencies import get_current_active_user
from .models import (
    StoreUpdate, StoreResponse, StoreWithStats, BulkStoreOperation,
    StoreManualCreate, StoreConnectionResult, StoreSyncRequest
)
from .service import StoreService

router = APIRouter(prefix="/stores", tags=["stores"])
store_service = StoreService()

@router.post("/manual", response_model=StoreResponse, status_code=status.HTTP_201_CREATED)
async def create_store_manual(
    store_data: StoreManualCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new store connection using manual method (shop URL + access token)
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"Creating store for user {current_user.id} with shop URL: {store_data.shop_url}")
        store = store_service.create_store_manual(db, current_user.id, store_data)
        logger.info(f"Store created successfully with ID: {store.id}")
        return StoreResponse.from_orm(store)
    except ValueError as e:
        logger.error(f"ValueError in store creation: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in store creation: {str(e)}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to create store: {str(e)}")

@router.post("/test-connection", response_model=StoreConnectionResult)
async def test_store_connection(
    connection_test: StoreManualCreate,
    current_user: User = Depends(get_current_active_user)
):
    """
    Test connection to a Shopify store
    """
    result = store_service.test_store_connection(
        connection_test.shop_url,
        connection_test.access_token
    )
    return result

@router.get("/", response_model=List[StoreWithStats])
async def get_user_stores(
    include_stats: bool = Query(False, description="Include store statistics"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all stores for the current user
    """
    stores = store_service.get_user_stores(db, current_user.id, include_stats)
    return [StoreWithStats.from_orm(store) for store in stores]

@router.get("/{store_id}", response_model=StoreWithStats)
async def get_store(
    store_id: int,
    include_stats: bool = Query(True, description="Include store statistics"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific store by ID
    """
    store = store_service.get_store_by_id(db, store_id, current_user.id)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    if include_stats:
        store.stats = store_service.get_store_stats(store)
        # Get assistant info
        from database.crud import AssistantCRUD
        assistant = AssistantCRUD.get_assistant_by_store(db, store.id)
        if assistant:
            store.assistant_status = assistant.status
            store.assistant_name = assistant.assistant_name

    return StoreWithStats.from_orm(store)

@router.put("/{store_id}", response_model=StoreResponse)
async def update_store(
    store_id: int,
    update_data: StoreUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update store information
    """
    store = store_service.update_store(db, store_id, current_user.id, update_data)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    return StoreResponse.from_orm(store)

@router.delete("/{store_id}")
async def delete_store(
    store_id: int,
    permanent: bool = Query(False, description="Permanently delete store"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a store (soft delete by default)
    """
    success = store_service.delete_store(db, store_id, current_user.id, soft_delete=not permanent)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    return {"message": "Store deleted successfully"}

@router.post("/{store_id}/sync")
async def sync_store_data(
    store_id: int,
    sync_request: Optional[StoreSyncRequest] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Sync store data from Shopify
    """
    force_sync = sync_request.force_sync if sync_request else False
    success = store_service.sync_store_data(db, store_id, current_user.id, force=force_sync)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found or sync failed")

    return {"message": "Store data synced successfully"}

@router.post("/bulk-operation")
async def bulk_store_operation(
    operation_data: BulkStoreOperation,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Perform bulk operations on multiple stores
    """
    results = store_service.bulk_operation(
        db, current_user.id, operation_data.store_ids, operation_data.operation
    )
    return results

# Store statistics endpoint
@router.get("/{store_id}/stats")
async def get_store_stats(
    store_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed statistics for a store
    """
    store = store_service.get_store_by_id(db, store_id, current_user.id)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    stats = store_service.get_store_stats(store)
    return stats

# Store health check endpoint
@router.get("/{store_id}/health")
async def check_store_health(
    store_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Check store connection health
    """
    store = store_service.get_store_by_id(db, store_id, current_user.id)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    from database.crud import StoreCRUD
    try:
        access_token = StoreCRUD.decode_token(store.access_token_encrypted)
        result = store_service.test_store_connection(store.shop_url, access_token)

        # Update store status based on health check
        if result.success:
            store_service.update_store(db, store_id, current_user.id, StoreUpdate(status='active'))
        else:
            store_service.update_store(db, store_id, current_user.id, StoreUpdate(status='error'))

        return {
            "store_id": store_id,
            "healthy": result.success,
            "message": result.message,
            "last_checked": store.last_sync
        }

    except Exception as e:
        store_service.update_store(db, store_id, current_user.id, StoreUpdate(status='error'))
        return {
            "store_id": store_id,
            "healthy": False,
            "message": "Health check failed",
            "error": str(e)
        }
