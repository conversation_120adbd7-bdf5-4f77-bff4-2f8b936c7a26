import os
import requests
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ShopifyAPIClient:
    """Shopify API client for making authenticated requests"""

    def __init__(self, shop_url: str, access_token: str):
        # Validate and normalize the shop URL
        self.shop_url = validate_shopify_domain(shop_url)
        self.access_token = access_token
        self.api_version = os.getenv("SHOPIFY_API_VERSION", "2025-01")
        self.base_url = f"https://{self.shop_url}/admin/api/{self.api_version}"

        self.headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }

    def test_connection(self) -> Dict[str, Any]:
        """Test the connection to Shopify store using GraphQL only"""
        try:
            # Use a simple GraphQL query to test connection
            simple_query = """
            {
              shop {
                id
                name
                myshopifyDomain
                email
              }
            }
            """

            url = f"{self.base_url}/graphql.json"
            payload = {'query': simple_query}

            response = requests.post(url, json=payload, headers=self.headers, timeout=30)

            # Handle HTTP errors with specific messages
            if response.status_code == 401:
                return {
                    'success': False,
                    'error': 'Invalid access token',
                    'message': 'Invalid access token - check your Shopify private app credentials'
                }
            elif response.status_code == 404:
                return {
                    'success': False,
                    'error': 'Store not found',
                    'message': 'Store not found - check your shop URL'
                }
            elif response.status_code == 403:
                return {
                    'success': False,
                    'error': 'Access forbidden',
                    'message': 'Access forbidden - check your app permissions'
                }
            elif response.status_code != 200:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'message': f'HTTP error {response.status_code}: {response.text[:100]}'
                }

            # Parse JSON response
            try:
                result = response.json()
            except ValueError:
                return {
                    'success': False,
                    'error': 'Invalid JSON response',
                    'message': 'Invalid JSON response from Shopify'
                }

            # Check for GraphQL errors
            if 'errors' in result:
                error_messages = [error.get('message', 'Unknown error') for error in result['errors']]
                return {
                    'success': False,
                    'error': f"GraphQL errors: {'; '.join(error_messages)}",
                    'message': 'GraphQL connection failed'
                }

            # Check for successful data
            if 'data' in result and 'shop' in result['data'] and result['data']['shop']:
                return {
                    'success': True,
                    'shop_data': result['data']['shop'],
                    'message': 'Connection successful'
                }
            else:
                return {
                    'success': False,
                    'error': 'No shop data in response',
                    'message': 'No shop data returned from GraphQL query'
                }

        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': 'Connection error',
                'message': 'Connection error - check your internet connection and shop URL'
            }
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Request timeout',
                'message': 'Request timeout - Shopify API is not responding'
            }
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'Request failed: {str(e)}',
                'message': f'Request failed: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Connection failed: {str(e)}'
            }

    def get_shop_info_graphql(self) -> Dict[str, Any]:
        """Get shop information using GraphQL"""
        query = """
        {
          shop {
            id
            name
            email
            myshopifyDomain
            primaryDomain { host }
            currencyCode
            ianaTimezone
            billingAddress {
              phone
              city
              country
              countryCodeV2
              latitude
              longitude
            }
            createdAt
          }
        }
        """

        return self._make_graphql_request(query)

    def get_products_count(self) -> int:
        """Get total number of products using GraphQL"""
        try:
            query = """
            {
              productsCount {
                count
              }
            }
            """
            result = self._make_graphql_request(query)
            return result.get('productsCount', {}).get('count', 0)
        except:
            return 0

    def get_orders_count(self) -> int:
        """Get total number of orders using GraphQL"""
        try:
            query = """
            {
              ordersCount {
                count
              }
            }
            """
            result = self._make_graphql_request(query)
            return result.get('ordersCount', {}).get('count', 0)
        except:
            return 0

    def get_customers_count(self) -> int:
        """Get total number of customers using GraphQL"""
        try:
            query = """
            {
              customersCount {
                count
              }
            }
            """
            result = self._make_graphql_request(query)
            return result.get('customersCount', {}).get('count', 0)
        except:
            return 0

    def get_latest_order(self) -> Optional[Dict[str, Any]]:
        """Get the latest order using GraphQL"""
        try:
            query = """
            {
              orders(first: 1, sortKey: CREATED_AT, reverse: true) {
                edges {
                  node {
                    id
                    name
                    createdAt
                    totalPriceSet {
                      shopMoney {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
            """
            result = self._make_graphql_request(query)
            orders = result.get('orders', {}).get('edges', [])
            if orders:
                return orders[0]['node']
            return None
        except:
            return None

    def _make_graphql_request(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Make GraphQL request to Shopify"""
        url = f"{self.base_url}/graphql.json"

        payload = {'query': query}
        if variables:
            payload['variables'] = variables

        try:
            response = requests.post(url, json=payload, headers=self.headers, timeout=30)

            # Check HTTP status
            if response.status_code == 401:
                raise Exception("Invalid access token - check your Shopify private app credentials")
            elif response.status_code == 404:
                raise Exception("Store not found - check your shop URL")
            elif response.status_code == 403:
                raise Exception("Access forbidden - check your app permissions")

            response.raise_for_status()

            result = response.json()

            # Check for GraphQL errors
            if 'errors' in result:
                error_messages = []
                for error in result['errors']:
                    error_messages.append(error.get('message', 'Unknown GraphQL error'))
                raise Exception(f"GraphQL errors: {'; '.join(error_messages)}")

            # Return the data portion
            return result.get('data', {})

        except requests.exceptions.ConnectionError:
            raise Exception("Connection error - check your internet connection and shop URL")
        except requests.exceptions.Timeout:
            raise Exception("Request timeout - Shopify API is not responding")
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {str(e)}")
        except ValueError as e:
            raise Exception(f"Invalid JSON response: {str(e)}")



def validate_shopify_domain(domain: str) -> str:
    """Validate and normalize Shopify domain"""
    domain = domain.strip().lower()

    # Remove protocol if present
    if domain.startswith('http://') or domain.startswith('https://'):
        domain = domain.split('://', 1)[1]

    # Remove trailing slash
    domain = domain.rstrip('/')

    # Ensure it ends with .myshopify.com
    if not domain.endswith('.myshopify.com'):
        if '.' not in domain:
            domain = f"{domain}.myshopify.com"
        else:
            raise ValueError('Invalid Shopify domain format')

    return domain

def extract_shop_name(shop_url: str) -> str:
    """Extract shop name from Shopify URL"""
    shop_url = validate_shopify_domain(shop_url)
    return shop_url.replace('.myshopify.com', '')

def generate_store_id(shop_url: str) -> str:
    """Generate a unique store ID from shop URL"""
    shop_name = extract_shop_name(shop_url)
    # Add a hash for uniqueness
    import hashlib
    hash_suffix = hashlib.md5(shop_url.encode()).hexdigest()[:8]
    return f"{shop_name}_{hash_suffix}"
