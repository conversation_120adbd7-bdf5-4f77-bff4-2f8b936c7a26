"""
Pinecone Assistant <PERSON><PERSON><PERSON>

Handles Pinecone AI assistant creation, management, and interaction
for e-commerce data processing and customer support.
"""

import os
import uuid
import hashlib
import re
import html
import logging
from io import BytesIO
from typing import Tuple, Optional, Dict, List
from pinecone import Pinecone
from pinecone_plugins.assistant.models.chat import Message
from dotenv import load_dotenv

# Import our custom modules
from shopify_api import ShopifyGraphQLClient, fetch_products, fetch_orders, fetch_customers
from utils.pinecone_limits_manager import PineconeLimitsManager
from database.models import calculate_data_hash

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class PineconeAssistant:
    """
    Pinecone Assistant for e-commerce data processing and customer support
    """

    def __init__(self, assistant_name: Optional[str] = None,
                 shop_url: Optional[str] = None,
                 access_token: Optional[str] = None) -> None:
        """
        Initialize Pinecone Assistant with dynamic configuration.

        Args:
            assistant_name: Name for the Pinecone assistant (optional)
            shop_url: Shopify store URL (optional, for backward compatibility)
            access_token: Shopify access token (optional, for backward compatibility)

        Raises:
            ValueError: If PINECONE_API_KEY is not set
        """
        # Initialize Pinecone
        pinecone_api_key = os.getenv("PINECONE_API_KEY")
        if not pinecone_api_key:
            raise ValueError("PINECONE_API_KEY environment variable is not set")

        # Initialize Pinecone client
        self.pc = Pinecone(api_key=pinecone_api_key)

        # Set assistant name (use provided name or default)
        self.assistant_name = assistant_name or os.getenv("PINECONE_ASSISTANT_NAME", "ecommerce-assistant")

        # Initialize Pinecone limits manager for Standard plan
        self.limits_manager = PineconeLimitsManager(plan_type="standard")

        # Store conversations
        self.conversations: Dict[str, List[Dict[str, str]]] = {}

        # Initialize Shopify GraphQL client (if credentials provided)
        self.shopify_client: Optional[ShopifyGraphQLClient] = None
        if shop_url and access_token:
            try:
                self.shopify_client = ShopifyGraphQLClient(shop_url=shop_url, access_token=access_token)
            except Exception as e:
                logger.error(f"Failed to initialize Shopify client: {str(e)}")
                raise
        elif shop_url or access_token:
            # For backward compatibility, try to load from environment
            try:
                self.shopify_client = ShopifyGraphQLClient()
            except ValueError as e:
                logger.warning(f"Shopify credentials not fully provided: {str(e)}")

        # Initialize or get the assistant (only if we have Shopify client)
        self.assistant = None
        if self.shopify_client:
            self._initialize_assistant()
        else:
            logger.info(f"Assistant '{self.assistant_name}' created without Shopify integration.")

    def _initialize_assistant(self):
        """Initialize or get the Pinecone Assistant."""
        try:
            # Get list of assistants
            assistants = self.pc.assistant.list_assistants()

            # Check if our assistant exists
            assistant_exists = False
            for assistant in assistants:
                if assistant.name == self.assistant_name:
                    self.assistant = self.pc.assistant.Assistant(assistant_name=self.assistant_name)
                    print(f"Connected to existing Pinecone assistant: {self.assistant_name}")
                    assistant_exists = True
                    break

            # If the assistant doesn't exist, create it
            if not assistant_exists:
                print(f"Creating new Pinecone assistant: {self.assistant_name}")
                self._create_new_assistant()
            else:
                print(f"Reusing existing Pinecone assistant: {self.assistant_name}")
        except Exception as e:
            print(f"Error initializing assistant: {str(e)}")
            raise

    def _create_new_assistant(self, products_data=None, orders_data=None, customers_data=None):
        """
        Create a new assistant and upload knowledge base files using chunked sync logic only.

        Args:
            products_data: Optional pre-fetched products data
            orders_data: Optional pre-fetched orders data
            customers_data: Optional pre-fetched customers data

        If data parameters are provided, they will be used directly.
        If not provided, data will be fetched from Shopify.
        """
        from database.crud import AssistantCRUD
        from database.session import SessionLocal
        from database.models import calculate_data_hash
        db = SessionLocal()
        try:
            self.assistant = self.pc.assistant.create_assistant(
                assistant_name=self.assistant_name,
                instructions="""You are a helpful e-commerce shopping assistant that provides detailed information about products, orders, and customers in our store.

When responding about products:
1. Always include specific product details like name, price, sizes, and materials when available
2. Format your responses with clear sections, bullet points, and proper spacing for readability
3. Highlight key features and specifications
4. Include pricing information when available
5. Suggest similar or complementary products when appropriate
6. Be friendly and helpful, focusing on helping the customer find what they need

When responding about orders:
1. Provide order details including order number, date, status, and items purchased
2. Include shipping and tracking information when available
3. Provide information about refunds if applicable
4. Be precise with financial information like prices, taxes, and totals
5. Format order information in a clear, organized manner

When responding about customers:
1. Provide customer information including name, contact details, and order history
2. Include address information when relevant
3. Mention total spent and number of orders
4. Respect customer privacy by not sharing sensitive information unnecessarily
5. Format customer information in a clear, organized manner

Follow guidelines for asking follow-up questions when appropriate. If the information is not in your knowledge base, say you don't know but offer to help find similar products, order information, or customer details.""",
                region="us",
                timeout=30
            )

            # Use chunked sync logic for initial upload
            if all([products_data, orders_data, customers_data]):
                print("DEBUG: Using provided data for initial assistant creation...")
                # Calculate hashes before sync
                products_hash = calculate_data_hash(products_data)
                orders_hash = calculate_data_hash(orders_data)
                customers_hash = calculate_data_hash(customers_data)

                # Perform sync first (don't update DB hashes yet)
                sync_result = self.selective_sync(
                    products_data=products_data,
                    orders_data=orders_data,
                    customers_data=customers_data,
                    sync_products=True,
                    sync_orders=True,
                    sync_customers=True,
                    update_db_hashes=False
                )

                # Only update database hashes if sync was successful
                if sync_result and sync_result.get("success"):
                    assistant_record = AssistantCRUD.get_assistant_by_name(db, self.assistant_name)
                    if assistant_record:
                        print("DEBUG: Updating database with initial data hashes...")
                        AssistantCRUD.update_sync_status(
                            db,
                            assistant_record.id,
                            products_hash=products_hash,
                            orders_hash=orders_hash,
                            customers_hash=customers_hash
                        )
                        print("DEBUG: Database hashes updated successfully")
                    else:
                        print("WARNING: Assistant record not found in database after creation")
                else:
                    print("WARNING: Initial sync failed, database hashes not updated")
            else:
                # Fetch data from Shopify and use chunked sync
                print("DEBUG: Fetching data from Shopify for initial assistant creation...")
                products_data = fetch_products(self.shopify_client)
                orders_data = fetch_orders(self.shopify_client)
                customers_data = fetch_customers(self.shopify_client)

                # Calculate hashes before sync
                products_hash = calculate_data_hash(products_data)
                orders_hash = calculate_data_hash(orders_data)
                customers_hash = calculate_data_hash(customers_data)

                # Perform sync first (don't update DB hashes yet)
                sync_result = self.selective_sync(
                    products_data=products_data,
                    orders_data=orders_data,
                    customers_data=customers_data,
                    sync_products=True,
                    sync_orders=True,
                    sync_customers=True,
                    update_db_hashes=False
                )

                # Only update database hashes if sync was successful
                if sync_result and sync_result.get("success"):
                    assistant_record = AssistantCRUD.get_assistant_by_name(db, self.assistant_name)
                    if assistant_record:
                        print("DEBUG: Updating database with initial data hashes...")
                        AssistantCRUD.update_sync_status(
                            db,
                            assistant_record.id,
                            products_hash=products_hash,
                            orders_hash=orders_hash,
                            customers_hash=customers_hash
                        )
                        print("DEBUG: Database hashes updated successfully")
                    else:
                        print("WARNING: Assistant record not found in database after creation")
                else:
                    print("WARNING: Initial sync failed, database hashes not updated")
        finally:
            db.close()

    def delete_assistant(self):
        """Delete the current assistant."""
        try:
            # Get all assistants
            assistants = self.pc.assistant.list_assistants()

            # Find and delete our assistant
            for assistant in assistants:
                if assistant.name == self.assistant_name:
                    self.pc.assistant.delete_assistant(assistant_name=self.assistant_name)
                    print(f"Deleted assistant: {self.assistant_name}")
                    return True

            print(f"Assistant not found: {self.assistant_name}")
            return False
        except Exception as e:
            print(f"Error deleting assistant: {str(e)}")
            return False

    def selective_sync(self, products_data=None, orders_data=None, customers_data=None,
                      sync_products=False, sync_orders=False, sync_customers=False,
                      existing_chunk_hashes=None, update_db_hashes=True):
        """
        Chunk-level selective sync - only updates the specific chunks that changed.

        Args:
            products_data: Products data to sync (if sync_products=True)
            orders_data: Orders data to sync (if sync_orders=True)
            customers_data: Customers data to sync (if sync_customers=True)
            sync_products: Whether to sync products data
            sync_orders: Whether to sync orders data
            sync_customers: Whether to sync customers data
            existing_chunk_hashes: Dict with existing chunk hashes for comparison

        Returns:
            dict: Results of chunk-level sync operations
        """
        try:
            from database.models import calculate_data_hash
            from database.crud import AssistantCRUD
            from database.session import SessionLocal

            print(f"DEBUG: Starting chunk-level selective sync for {self.assistant_name}")
            print(f"DEBUG: Sync flags - Products: {sync_products}, Orders: {sync_orders}, Customers: {sync_customers}")

            # Ensure assistant exists
            if not self.assistant:
                print("DEBUG: Assistant doesn't exist, creating new one...")
                self._create_new_assistant()
                return {"success": True, "message": "New assistant created with all data"}

            # Clean up any duplicate files before starting sync
            print("DEBUG: Cleaning up duplicate files before sync...")
            self._cleanup_duplicate_files()

            sync_results = {
                "success": True,
                "synced_files": [],
                "errors": [],
                "chunk_operations": {},
                "efficiency_stats": {}
            }

            # Initialize existing chunk hashes if not provided
            if existing_chunk_hashes is None:
                existing_chunk_hashes = {"products": {}, "orders": {}, "customers": {}}

            # Calculate overall hashes for change detection (only if update_db_hashes is True)
            if update_db_hashes:
                db = SessionLocal()
                try:
                    assistant_record = AssistantCRUD.get_assistant_by_name(db, self.assistant_name)
                    if assistant_record:
                        # Calculate new hashes
                        new_hashes = {}
                        if sync_products and products_data:
                            new_hashes['products'] = calculate_data_hash(products_data)
                        if sync_orders and orders_data:
                            new_hashes['orders'] = calculate_data_hash(orders_data)
                        if sync_customers and customers_data:
                            new_hashes['customers'] = calculate_data_hash(customers_data)

                        # Update database with new hashes
                        update_data = {}
                        if 'products' in new_hashes:
                            update_data['products_hash'] = new_hashes['products']
                        if 'orders' in new_hashes:
                            update_data['orders_hash'] = new_hashes['orders']
                        if 'customers' in new_hashes:
                            update_data['customers_hash'] = new_hashes['customers']

                        if update_data:
                            AssistantCRUD.update_sync_status(db, assistant_record.id, **update_data)
                            print(f"DEBUG: Updated database hashes during sync: {list(update_data.keys())}")
                finally:
                    db.close()
            else:
                print("DEBUG: Skipping database hash updates during initial creation")

            # Sync products if requested
            if sync_products and products_data:
                try:
                    print("DEBUG: Starting chunk-level sync for products...")
                    products_text = self._format_products_for_assistant(products_data)

                    # Detect changed chunks
                    chunk_analysis = self._detect_changed_chunks(
                        new_content=products_text,
                        data_type="products",
                        existing_chunk_hashes=existing_chunk_hashes.get("products", {})
                    )

                    # Replace only changed chunks
                    chunk_result = self._replace_specific_chunks(
                        data_type="products",
                        source="products_catalog",
                        chunk_analysis=chunk_analysis
                    )

                    if chunk_result.get("fallback_required"):
                        # Fallback to full replacement
                        print("DEBUG: Falling back to full products replacement")
                        self._delete_data_type_files("products")
                        self._upload_products_data(products_data)
                        sync_results["chunk_operations"]["products"] = "full_replacement"
                    else:
                        sync_results["chunk_operations"]["products"] = chunk_result["operations_performed"]
                        sync_results["efficiency_stats"]["products"] = {
                            "unchanged_chunks": chunk_result["unchanged_chunks"],
                            "total_operations": chunk_result["total_operations"]
                        }

                    sync_results["synced_files"].append("products")
                    print("DEBUG: Products chunk-level sync completed")

                except Exception as e:
                    error_msg = f"Products chunk-level sync failed: {str(e)}"
                    print(f"ERROR: {error_msg}")
                    sync_results["errors"].append(error_msg)

            # Sync orders if requested
            if sync_orders and orders_data:
                try:
                    print("DEBUG: Starting chunk-level sync for orders...")
                    orders_text = self._format_orders_for_assistant(orders_data)

                    # Detect changed chunks
                    chunk_analysis = self._detect_changed_chunks(
                        new_content=orders_text,
                        data_type="orders",
                        existing_chunk_hashes=existing_chunk_hashes.get("orders", {})
                    )

                    # Replace only changed chunks
                    chunk_result = self._replace_specific_chunks(
                        data_type="orders",
                        source="orders_data",
                        chunk_analysis=chunk_analysis
                    )

                    if chunk_result.get("fallback_required"):
                        # Fallback to full replacement
                        print("DEBUG: Falling back to full orders replacement")
                        self._delete_data_type_files("orders")
                        self._upload_orders_data(orders_data)
                        sync_results["chunk_operations"]["orders"] = "full_replacement"
                    else:
                        sync_results["chunk_operations"]["orders"] = chunk_result["operations_performed"]
                        sync_results["efficiency_stats"]["orders"] = {
                            "unchanged_chunks": chunk_result["unchanged_chunks"],
                            "total_operations": chunk_result["total_operations"]
                        }

                    sync_results["synced_files"].append("orders")
                    print("DEBUG: Orders chunk-level sync completed")

                except Exception as e:
                    error_msg = f"Orders chunk-level sync failed: {str(e)}"
                    print(f"ERROR: {error_msg}")
                    sync_results["errors"].append(error_msg)

            # Sync customers if requested
            if sync_customers and customers_data:
                try:
                    print("DEBUG: Starting chunk-level sync for customers...")
                    customers_text = self._format_customers_for_assistant(customers_data)

                    # Detect changed chunks
                    chunk_analysis = self._detect_changed_chunks(
                        new_content=customers_text,
                        data_type="customers",
                        existing_chunk_hashes=existing_chunk_hashes.get("customers", {})
                    )

                    # Replace only changed chunks
                    chunk_result = self._replace_specific_chunks(
                        data_type="customers",
                        source="customers_data",
                        chunk_analysis=chunk_analysis
                    )

                    if chunk_result.get("fallback_required"):
                        # Fallback to full replacement
                        print("DEBUG: Falling back to full customers replacement")
                        self._delete_data_type_files("customers")
                        self._upload_customers_data(customers_data)
                        sync_results["chunk_operations"]["customers"] = "full_replacement"
                    else:
                        sync_results["chunk_operations"]["customers"] = chunk_result["operations_performed"]
                        sync_results["efficiency_stats"]["customers"] = {
                            "unchanged_chunks": chunk_result["unchanged_chunks"],
                            "total_operations": chunk_result["total_operations"]
                        }

                    sync_results["synced_files"].append("customers")
                    print("DEBUG: Customers chunk-level sync completed")

                except Exception as e:
                    error_msg = f"Customers chunk-level sync failed: {str(e)}"
                    print(f"ERROR: {error_msg}")
                    sync_results["errors"].append(error_msg)

            # Determine overall success and create summary
            if sync_results["errors"]:
                sync_results["success"] = len(sync_results["synced_files"]) > 0
                sync_results["message"] = f"Partial chunk-level sync completed. Synced: {sync_results['synced_files']}, Errors: {len(sync_results['errors'])}"
            else:
                # Calculate efficiency summary
                total_operations = sum(stats.get("total_operations", 0) for stats in sync_results["efficiency_stats"].values())
                total_unchanged = sum(stats.get("unchanged_chunks", 0) for stats in sync_results["efficiency_stats"].values())

                if total_operations + total_unchanged > 0:
                    efficiency_percent = (total_unchanged / (total_operations + total_unchanged)) * 100
                    sync_results["message"] = f"Chunk-level sync completed successfully. Synced: {sync_results['synced_files']}, Efficiency: {efficiency_percent:.1f}%"
                else:
                    sync_results["message"] = f"Chunk-level sync completed successfully. Synced: {sync_results['synced_files']}"

            print(f"DEBUG: Chunk-level selective sync completed - {sync_results['message']}")
            return sync_results

        except Exception as e:
            print(f"ERROR: Chunk-level selective sync failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "synced_files": [],
                "errors": [f"Chunk-level selective sync failed: {str(e)}"],
                "message": "Chunk-level selective sync failed"
            }

    def _cleanup_duplicate_files(self) -> int:
        """
        Clean up duplicate files that might exist from previous failed syncs.
        This method identifies and removes older duplicate files for each data type.

        Returns:
            int: Number of duplicate files removed
        """
        try:
            files = self.assistant.list_files()
            deleted_count = 0

            # Group files by data type and chunk number
            file_groups = {}

            for file in files:
                # Parse filename to extract data type and chunk info
                if file.name.startswith("formatted_") and file.name.endswith(".txt"):
                    parts = file.name.replace("formatted_", "").replace(".txt", "").split("_part_")
                    if len(parts) == 2:
                        data_type = parts[0]
                        try:
                            chunk_num = int(parts[1])
                            key = f"{data_type}_{chunk_num}"
                        except ValueError:
                            continue
                    elif len(parts) == 1:
                        data_type = parts[0]
                        key = f"{data_type}_single"
                    else:
                        continue

                    if key not in file_groups:
                        file_groups[key] = []
                    file_groups[key].append(file)

            # For each group, keep only the newest file and delete the rest
            for key, file_list in file_groups.items():
                if len(file_list) > 1:
                    # Sort by creation time (newest first)
                    file_list.sort(key=lambda f: f.created_on, reverse=True)

                    # Delete all but the newest file
                    for file_to_delete in file_list[1:]:
                        try:
                            print(f"DEBUG: Removing duplicate file: {file_to_delete.name} (ID: {file_to_delete.id})")
                            self.assistant.delete_file(file_id=file_to_delete.id)
                            deleted_count += 1
                        except Exception as e:
                            print(f"WARNING: Failed to delete duplicate file {file_to_delete.name}: {str(e)}")

            if deleted_count > 0:
                print(f"DEBUG: Cleaned up {deleted_count} duplicate files")
            else:
                print("DEBUG: No duplicate files found")

            return deleted_count

        except Exception as e:
            print(f"WARNING: Failed to cleanup duplicate files: {str(e)}")
            return 0

    def _delete_data_type_files(self, data_type: str) -> int:
        """
        Delete all files for a specific data type (products, orders, customers).
        Handles both single files and chunked files.

        Args:
            data_type: The type of data ('products', 'orders', 'customers')

        Returns:
            int: Number of files deleted
        """
        try:
            # List all files in the assistant
            files = self.assistant.list_files()
            deleted_count = 0

            # Find all files that belong to this data type
            files_to_delete = self._get_files_by_data_type(files, data_type)

            # Delete each file
            for file in files_to_delete:
                try:
                    print(f"DEBUG: Deleting {data_type} file: {file.name} (ID: {file.id})")
                    self.assistant.delete_file(file_id=file.id)
                    deleted_count += 1
                    print(f"DEBUG: Successfully deleted file: {file.name}")
                except Exception as e:
                    print(f"WARNING: Failed to delete file {file.name}: {str(e)}")
                    # Continue with other files even if one fails

            if deleted_count == 0:
                print(f"DEBUG: No {data_type} files found to delete")
            else:
                print(f"DEBUG: Successfully deleted {deleted_count} {data_type} files")

            return deleted_count

        except Exception as e:
            print(f"WARNING: Failed to delete {data_type} files: {str(e)}")
            # Don't raise exception, just log warning and continue
            return 0

    def _get_files_by_data_type(self, files, data_type: str) -> List:
        """
        Get all files that belong to a specific data type.

        Args:
            files: List of files from assistant.list_files()
            data_type: The type of data ('products', 'orders', 'customers')

        Returns:
            List of files that match the data type
        """
        matching_files = []

        for file in files:
            # Check if file matches the data type based on filename patterns
            if self._is_data_type_file(file.name, data_type):
                matching_files.append(file)

        return matching_files

    def _is_data_type_file(self, filename: str, data_type: str) -> bool:
        """
        Check if a filename belongs to a specific data type.

        Args:
            filename: Name of the file
            data_type: The type of data ('products', 'orders', 'customers')

        Returns:
            bool: True if the file belongs to the data type
        """
        # Handle single files
        if filename == f"formatted_{data_type}.txt":
            return True

        # Handle chunked files (e.g., formatted_products_part_1.txt)
        if filename.startswith(f"formatted_{data_type}_part_") and filename.endswith(".txt"):
            return True

        return False

    def _detect_changed_chunks(self, new_content: str, data_type: str, existing_chunk_hashes: Dict[int, str]) -> Dict:
        """
        Detect which chunks have changed by comparing content hashes.

        Args:
            new_content: The new content to be chunked
            data_type: Type of data (products, orders, customers)
            existing_chunk_hashes: Dict mapping chunk_number -> hash from previous upload

        Returns:
            Dict with changed_chunks, new_chunks, and chunk_metadata
        """
        try:
            # Split new content into chunks using the same logic
            max_chunk_size_bytes = int(9.5 * 1024 * 1024)  # Same as default
            new_chunks = self._split_content_intelligently(new_content, max_chunk_size_bytes)

            # Calculate hashes for new chunks
            new_chunk_hashes = {}
            for i, chunk in enumerate(new_chunks, 1):
                chunk_hash = hashlib.md5(chunk.encode('utf-8')).hexdigest()
                new_chunk_hashes[i] = chunk_hash

            # Compare with existing hashes to find changes
            changed_chunks = []
            new_chunks_list = []
            unchanged_chunks = []

            # Check each new chunk against existing
            for chunk_num, new_hash in new_chunk_hashes.items():
                if chunk_num in existing_chunk_hashes:
                    if existing_chunk_hashes[chunk_num] != new_hash:
                        changed_chunks.append(chunk_num)
                    else:
                        unchanged_chunks.append(chunk_num)
                else:
                    new_chunks_list.append(chunk_num)

            # Check for deleted chunks (existed before but not now)
            deleted_chunks = []
            for chunk_num in existing_chunk_hashes:
                if chunk_num not in new_chunk_hashes:
                    deleted_chunks.append(chunk_num)

            result = {
                "changed_chunks": changed_chunks,
                "new_chunks": new_chunks_list,
                "deleted_chunks": deleted_chunks,
                "unchanged_chunks": unchanged_chunks,
                "new_chunk_hashes": new_chunk_hashes,
                "chunks_content": {i: chunk for i, chunk in enumerate(new_chunks, 1)},
                "total_new_chunks": len(new_chunks)
            }

            print(f"DEBUG: Chunk analysis for {data_type}:")
            print(f"  - Changed chunks: {changed_chunks}")
            print(f"  - New chunks: {new_chunks_list}")
            print(f"  - Deleted chunks: {deleted_chunks}")
            print(f"  - Unchanged chunks: {unchanged_chunks}")

            return result

        except Exception as e:
            print(f"ERROR: Failed to detect changed chunks for {data_type}: {str(e)}")
            # Return full replacement as fallback
            return {
                "changed_chunks": [],
                "new_chunks": [],
                "deleted_chunks": [],
                "unchanged_chunks": [],
                "new_chunk_hashes": {},
                "chunks_content": {},
                "total_new_chunks": 0,
                "error": str(e),
                "fallback_to_full_replacement": True
            }

    def _delete_existing_file(self, file_name):
        """Delete an existing file from the assistant by name."""
        try:
            # List all files in the assistant
            files = self.assistant.list_files()

            # Find and delete the specific file
            for file in files:
                if file.name == file_name:
                    print(f"DEBUG: Deleting existing file: {file_name} (ID: {file.id})")
                    self.assistant.delete_file(file_id=file.id)
                    print(f"DEBUG: Successfully deleted file: {file_name}")
                    return True

            print(f"DEBUG: File {file_name} not found, skipping deletion")
            return False

        except Exception as e:
            print(f"WARNING: Failed to delete existing file {file_name}: {str(e)}")
            # Don't raise exception, just log warning and continue
            return False

    def _update_file_ids_and_hash_in_db(self, data_type: str, file_ids: list, data):
        from database.crud import AssistantCRUD
        from database.session import SessionLocal
        from database.models import calculate_data_hash
        db = SessionLocal()
        try:
            assistant_record = AssistantCRUD.get_assistant_by_name(db, self.assistant_name)
            if assistant_record:
                field_map = {
                    'products': ('products_file_id', 'products_hash'),
                    'orders': ('orders_file_id', 'orders_hash'),
                    'customers': ('customers_file_id', 'customers_hash'),
                }
                file_field, hash_field = field_map.get(data_type)
                file_ids_str = ','.join(file_ids)
                data_hash = calculate_data_hash(data)
                update_kwargs = {file_field: file_ids_str, hash_field: data_hash}
                AssistantCRUD.update_assistant(db, assistant_record.id, **update_kwargs)
        finally:
            db.close()

    def _cleanup_old_files(self, data_type: str, current_file_ids: list):
        """
        Delete files in Pinecone for the given data type that are not in current_file_ids.
        """
        try:
            files = self.assistant.list_files()
            for file in files:
                if self._is_data_type_file(file.name, data_type) and file.id not in current_file_ids:
                    print(f"Deleting old {data_type} file: {file.name} (ID: {file.id})")
                    self.assistant.delete_file(file_id=file.id)
        except Exception as e:
            print(f"WARNING: Cleanup failed for {data_type}: {e}")

    def _upload_data_in_chunks(self, data_type: str, data, formatted_text: str, source: str):
        """
        Uploads data in chunks, returns list of file IDs.
        """
        max_chunk_size_mb = 9.5
        max_chunk_size_bytes = int(max_chunk_size_mb * 1024 * 1024)
        chunks = self._split_content_intelligently(formatted_text, max_chunk_size_bytes)
        file_ids = []
        for i, chunk in enumerate(chunks, 1):
            chunk_stream = BytesIO(chunk.encode("utf-8"))
            file_name = f"formatted_{data_type}_part_{i}.txt"
            response = self.assistant.upload_bytes_stream(
                stream=chunk_stream,
                file_name=file_name,
                metadata={
                    "source": source,
                    "type": data_type,
                    "chunk_number": i,
                    "total_chunks": len(chunks),
                },
                timeout=None
            )
            file_ids.append(response.id)
        # Update file IDs and hash in DB
        self._update_file_ids_and_hash_in_db(data_type, file_ids, data)
        # Cleanup old files
        self._cleanup_old_files(data_type, file_ids)
        return file_ids

    def _replace_specific_chunks(self, data_type: str, source: str, chunk_analysis: Dict) -> Dict:
        """
        Replace only the specific chunks that have changed.

        Args:
            data_type: Type of data (products, orders, customers)
            source: Source identifier for metadata
            chunk_analysis: Result from _detect_changed_chunks

        Returns:
            Dict with operation results
        """
        try:
            if chunk_analysis.get("fallback_to_full_replacement"):
                print(f"DEBUG: Falling back to full replacement for {data_type}")
                return {"success": False, "fallback_required": True}

            changed_chunks = chunk_analysis["changed_chunks"]
            new_chunks = chunk_analysis["new_chunks"]
            deleted_chunks = chunk_analysis["deleted_chunks"]
            chunks_content = chunk_analysis["chunks_content"]
            total_chunks = chunk_analysis["total_new_chunks"]

            operations_performed = []
            errors = []

            # Delete removed chunks first
            for chunk_num in deleted_chunks:
                try:
                    file_name = f"formatted_{data_type}_part_{chunk_num}.txt"
                    if self._delete_existing_file(file_name):
                        operations_performed.append(f"deleted_chunk_{chunk_num}")
                        print(f"DEBUG: Deleted chunk {chunk_num} for {data_type}")
                except Exception as e:
                    error_msg = f"Failed to delete chunk {chunk_num}: {str(e)}"
                    errors.append(error_msg)
                    print(f"ERROR: {error_msg}")

            # Replace changed chunks
            for chunk_num in changed_chunks:
                try:
                    file_name = f"formatted_{data_type}_part_{chunk_num}.txt"
                    chunk_content = chunks_content[chunk_num]
                    chunk_hash = hashlib.md5(chunk_content.encode('utf-8')).hexdigest()
                    chunk_size_mb = len(chunk_content.encode('utf-8')) / (1024 * 1024)

                    # Delete old chunk
                    self._delete_existing_file(file_name)

                    # Upload new chunk
                    chunk_stream = BytesIO(chunk_content.encode("utf-8"))
                    response = self.assistant.upload_bytes_stream(
                        stream=chunk_stream,
                        file_name=file_name,
                        metadata={
                            "source": source,
                            "type": data_type,
                            "chunk_number": chunk_num,
                            "total_chunks": total_chunks,
                            "file_size_mb": f"{chunk_size_mb:.2f}",
                            "chunk_hash": chunk_hash
                        },
                        timeout=None
                    )
                    operations_performed.append(f"replaced_chunk_{chunk_num}")
                    print(f"DEBUG: Replaced chunk {chunk_num} for {data_type} (hash: {chunk_hash[:8]}...)")
                except Exception as e:
                    error_msg = f"Failed to replace chunk {chunk_num}: {str(e)}"
                    errors.append(error_msg)
                    print(f"ERROR: {error_msg}")

            # Upload new chunks
            for chunk_num in new_chunks:
                try:
                    file_name = f"formatted_{data_type}_part_{chunk_num}.txt"
                    chunk_content = chunks_content[chunk_num]
                    chunk_hash = hashlib.md5(chunk_content.encode('utf-8')).hexdigest()
                    chunk_size_mb = len(chunk_content.encode('utf-8')) / (1024 * 1024)

                    chunk_stream = BytesIO(chunk_content.encode("utf-8"))
                    response = self.assistant.upload_bytes_stream(
                        stream=chunk_stream,
                        file_name=file_name,
                        metadata={
                            "source": source,
                            "type": data_type,
                            "chunk_number": chunk_num,
                            "total_chunks": total_chunks,
                            "file_size_mb": f"{chunk_size_mb:.2f}",
                            "chunk_hash": chunk_hash
                        },
                        timeout=None
                    )
                    operations_performed.append(f"added_chunk_{chunk_num}")
                    print(f"DEBUG: Added new chunk {chunk_num} for {data_type} (hash: {chunk_hash[:8]}...)")
                except Exception as e:
                    error_msg = f"Failed to add new chunk {chunk_num}: {str(e)}"
                    errors.append(error_msg)
                    print(f"ERROR: {error_msg}")

            # Summary
            total_operations = len(operations_performed)
            unchanged_count = len(chunk_analysis["unchanged_chunks"])

            print(f"DEBUG: Chunk-level sync completed for {data_type}:")
            print(f"  - Operations performed: {total_operations}")
            print(f"  - Chunks unchanged: {unchanged_count}")
            print(f"  - Errors: {len(errors)}")

            return {
                "success": len(errors) == 0,
                "operations_performed": operations_performed,
                "errors": errors,
                "unchanged_chunks": unchanged_count,
                "total_operations": total_operations,
                "chunk_hashes": chunk_analysis["new_chunk_hashes"]
            }

        except Exception as e:
            print(f"ERROR: Failed to replace specific chunks for {data_type}: {str(e)}")
            return {
                "success": False,
                "fallback_required": True,
                "error": str(e)
            }

    def _upload_large_file_in_chunks(self, content: str, data_type: str, source: str, max_chunk_size_mb: float = 9.5):
        """
        Split large files into chunks and upload them separately with individual chunk hashing.

        Args:
            content: The text content to upload
            data_type: Type of data (products, orders, customers)
            source: Source identifier for metadata
            max_chunk_size_mb: Maximum size per chunk in MB

        Returns:
            List[Dict]: List of chunk metadata including hashes
        """
        try:
            max_chunk_size_bytes = int(max_chunk_size_mb * 1024 * 1024)
            content_bytes = content.encode('utf-8')
            total_size_mb = len(content_bytes) / (1024 * 1024)

            print(f"DEBUG: Splitting {data_type} file ({total_size_mb:.2f} MB) into chunks...")

            # Split content into logical chunks (by sections if possible)
            chunks = self._split_content_intelligently(content, max_chunk_size_bytes)

            print(f"DEBUG: Created {len(chunks)} chunks for {data_type}")

            chunk_metadata_list = []

            # Upload each chunk with individual hashing
            for i, chunk in enumerate(chunks, 1):
                chunk_size_mb = len(chunk.encode('utf-8')) / (1024 * 1024)

                # Calculate hash for this specific chunk
                chunk_hash = hashlib.md5(chunk.encode('utf-8')).hexdigest()

                print(f"DEBUG: Uploading {data_type} chunk {i}/{len(chunks)} ({chunk_size_mb:.2f} MB, hash: {chunk_hash[:8]}...)...")

                chunk_stream = BytesIO(chunk.encode("utf-8"))
                response = self.assistant.upload_bytes_stream(
                    stream=chunk_stream,
                    file_name=f"formatted_{data_type}_part_{i}.txt",
                    metadata={
                        "source": source,
                        "type": data_type,
                        "chunk_number": i,
                        "total_chunks": len(chunks),
                        "file_size_mb": f"{chunk_size_mb:.2f}",
                        "chunk_hash": chunk_hash
                    },
                    timeout=None
                )

                # Store chunk metadata for future comparison
                chunk_metadata = {
                    "chunk_number": i,
                    "file_name": f"formatted_{data_type}_part_{i}.txt",
                    "file_id": response.id,
                    "chunk_hash": chunk_hash,
                    "size_mb": chunk_size_mb,
                    "data_type": data_type
                }
                chunk_metadata_list.append(chunk_metadata)

                print(f"DEBUG: {data_type} chunk {i} uploaded successfully. File ID: {response.id}")

            return chunk_metadata_list

        except Exception as e:
            print(f"ERROR: Failed to upload {data_type} chunks: {str(e)}")
            raise

    def _split_content_intelligently(self, content: str, max_chunk_size_bytes: int) -> List[str]:
        """
        Split content into chunks intelligently, trying to preserve logical sections.
        """
        chunks = []
        lines = content.split('\n')
        current_chunk = ""
        current_size = 0

        for line in lines:
            line_bytes = len((line + '\n').encode('utf-8'))

            # If adding this line would exceed the limit, start a new chunk
            if current_size + line_bytes > max_chunk_size_bytes and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = line + '\n'
                current_size = line_bytes
            else:
                current_chunk += line + '\n'
                current_size += line_bytes

        # Add the last chunk if it has content
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _replace_link(self, text: str) -> str:
        text = str(text) if text is not None else ""
        import re
        url_pattern = re.compile(r'(https?://\S+|www\.\S+)')
        def repl(match):
            url = match.group(0)
            href = url if url.startswith('http') else f'http://{url}'
            return f'<a href="{href}" target="_blank">{url}</a>'
        return url_pattern.sub(repl, text)

    def _clean_html_description(self, html_text: str) -> str:
        html_text = str(html_text) if html_text is not None else ""
        import re
        # Remove script/style tags
        cleaned = re.sub(r'<(script|style)[^>]*>.*?</\1>', '', html_text, flags=re.DOTALL | re.IGNORECASE)
        # Remove all HTML tags except <a>
        cleaned = re.sub(r'<(?!a\b)[^>]+>', '', cleaned)
        # Unescape HTML entities
        cleaned = html.unescape(cleaned)
        # Replace links
        cleaned = self._replace_link(cleaned)
        # Collapse whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned

    def _format_products_for_assistant(self, products_data):
        formatted_text = "# E-COMMERCE PRODUCT CATALOG\n\n"
        for product in products_data.get('products', []):
            formatted_text += f"## {self._replace_link(product.get('title', 'Untitled Product'))}\n"
            formatted_text += f"ID: {product.get('id')}\n"
            formatted_text += f"Vendor: {self._replace_link(product.get('vendor', 'Unknown'))}\n"
            formatted_text += f"Product Type: {self._replace_link(product.get('product_type', 'Not specified'))}\n"
            formatted_text += f"Tags: {self._replace_link(product.get('tags', ''))}\n\n"
            body_html = product.get('body_html', '')
            cleaned_description = self._clean_html_description(body_html)
            formatted_text += f"Description:\n{cleaned_description}\n\n"
            formatted_text += "Variants:\n"
            for variant in product.get('variants', []):
                formatted_text += f"- {self._replace_link(variant.get('title', 'Untitled Variant'))}: ${variant.get('price', '0.00')}\n"
                formatted_text += f"  SKU: {self._replace_link(variant.get('sku', 'No SKU'))}\n"
                formatted_text += f"  Inventory: {variant.get('inventory_quantity', 0)}\n"
            formatted_text += "\n---\n\n"
        return formatted_text

    def _format_orders_for_assistant(self, orders_data):
        formatted_text = "# E-COMMERCE ORDER INFORMATION\n\n"
        orders = []
        if isinstance(orders_data, dict) and 'orders' in orders_data:
            orders = orders_data['orders']
        elif isinstance(orders_data, list):
            orders = orders_data
        elif isinstance(orders_data, dict) and 'id' in orders_data:
            orders = [orders_data]
        else:
            return formatted_text
        for order in orders:
            formatted_text += f"## Order #{self._replace_link(str(order.get('order_number', 'Unknown')))}\n"
            formatted_text += f"Order ID: {order.get('id')}\n"
            formatted_text += f"Date: {self._replace_link(str(order.get('created_at', 'Unknown')))}\n"
            formatted_text += f"Status: Financial - {self._replace_link(str(order.get('financial_status', 'Unknown')))}, Fulfillment - {self._replace_link(str(order.get('fulfillment_status', 'Unfulfilled')))}\n"
            customer = order.get('customer', {}) or {}
            formatted_text += f"Customer: {self._replace_link(customer.get('first_name', ''))} {self._replace_link(customer.get('last_name', ''))}\n"
            formatted_text += f"Email: {self._replace_link(order.get('email', 'Not provided'))}\n"
            formatted_text += f"Phone: {self._replace_link(order.get('phone', 'Not provided'))}\n\n"
            formatted_text += f"Order Status URL: {self._replace_link(order.get('order_status_url', 'Not provided'))}\n\n"
            formatted_text += "### Financial Information\n"
            formatted_text += f"Currency: {self._replace_link(order.get('currency', 'Unknown'))}\n"
            formatted_text += f"Subtotal: {order.get('subtotal_price', '0.00')} {order.get('currency', '')}\n"
            shipping_amount = '0.00'
            total_shipping = order.get('total_shipping_price_set')
            if isinstance(total_shipping, dict) and isinstance(total_shipping.get('shop_money'), dict):
                shipping_amount = total_shipping['shop_money'].get('amount', '0.00')
            formatted_text += f"Shipping: {shipping_amount} {order.get('currency', '')}\n"
            formatted_text += f"Tax: {order.get('total_tax', '0.00')} {order.get('currency', '')}\n"
            formatted_text += f"Discounts: {order.get('total_discounts', '0.00')} {order.get('currency', '')}\n"
            formatted_text += f"Total: {order.get('total_price', '0.00')} {order.get('currency', '')}\n"
            formatted_text += f"Outstanding Balance: {order.get('total_outstanding', '0.00')} {order.get('currency', '')}\n\n"
            formatted_text += "### Items Ordered\n"
            for item in order.get('line_items', []):
                formatted_text += f"- {self._replace_link(item.get('name', 'Unknown Product'))}\n"
                formatted_text += f"  Quantity: {item.get('quantity', 0)}\n"
                formatted_text += f"  Price: {item.get('price', '0.00')} {order.get('currency', '')}\n"
                formatted_text += f"  SKU: {self._replace_link(item.get('sku', 'No SKU'))}\n"
                formatted_text += f"  Fulfillment Status: {self._replace_link(item.get('fulfillment_status', 'Unfulfilled'))}\n"
            formatted_text += "\n"
            if order.get('shipping_address'):
                addr = order.get('shipping_address')
                formatted_text += "### Shipping Address\n"
                formatted_text += f"{self._replace_link(addr.get('name', ''))}\n"
                if addr.get('company'):
                    formatted_text += f"{self._replace_link(addr.get('company'))}\n"
                formatted_text += f"{self._replace_link(addr.get('address1', ''))}\n"
                if addr.get('address2'):
                    formatted_text += f"{self._replace_link(addr.get('address2'))}\n"
                formatted_text += f"{self._replace_link(addr.get('city', ''))}, {self._replace_link(addr.get('province', ''))} {self._replace_link(addr.get('zip', ''))}\n"
                formatted_text += f"{self._replace_link(addr.get('country', ''))}\n"
                formatted_text += f"Phone: {self._replace_link(addr.get('phone', 'Not provided'))}\n\n"
            if order.get('fulfillments'):
                formatted_text += "### Fulfillment Information\n"
                for fulfillment in order.get('fulfillments', []):
                    formatted_text += f"Fulfillment #{self._replace_link(fulfillment.get('name', 'Unknown'))}\n"
                    formatted_text += f"Status: {self._replace_link(fulfillment.get('status', 'Unknown'))}\n"
                    formatted_text += f"Tracking Company: {self._replace_link(fulfillment.get('tracking_company', 'Not provided'))}\n"
                    formatted_text += f"Tracking Number: {self._replace_link(fulfillment.get('tracking_number', 'Not provided'))}\n"
                    if fulfillment.get('tracking_url'):
                        formatted_text += f"Tracking URL: {self._replace_link(fulfillment.get('tracking_url'))}\n"
                    formatted_text += "\n"
            if order.get('refunds'):
                formatted_text += "### Refund Information\n"
                for refund in order.get('refunds', []):
                    formatted_text += f"Refund ID: {refund.get('id')}\n"
                    formatted_text += f"Date: {self._replace_link(refund.get('created_at', 'Unknown'))}\n"
                    if refund.get('refund_line_items'):
                        formatted_text += "Refunded Items:\n"
                        for item in refund.get('refund_line_items', []):
                            line_item = item.get('line_item', {})
                            formatted_text += f"- {self._replace_link(line_item.get('name', 'Unknown Product'))}\n"
                            formatted_text += f"  Quantity: {item.get('quantity', 0)}\n"
                            formatted_text += f"  Subtotal: {item.get('subtotal', '0.00')} {order.get('currency', '')}\n"
                            formatted_text += f"  Tax: {item.get('total_tax', '0.00')} {order.get('currency', '')}\n"
                    formatted_text += "\n"
            formatted_text += "\n---\n\n"
        return formatted_text

    def _format_customers_for_assistant(self, customers_data):
        formatted_text = "# E-COMMERCE CUSTOMER INFORMATION\n\n"
        customers = []
        if isinstance(customers_data, dict) and 'customers' in customers_data:
            customers = customers_data['customers']
        elif isinstance(customers_data, list):
            customers = customers_data
        elif isinstance(customers_data, dict) and 'id' in customers_data:
            customers = [customers_data]
        else:
            return formatted_text
        for customer in customers:
            formatted_text += f"## Customer ID: {customer.get('id')}\n"
            formatted_text += "### Basic Information\n"
            formatted_text += f"Name: {self._replace_link(customer.get('first_name', ''))} {self._replace_link(customer.get('last_name', ''))}\n"
            formatted_text += f"Email: {self._replace_link(customer.get('email', 'Not provided'))}\n"
            formatted_text += f"Phone: {self._replace_link(customer.get('phone', 'Not provided'))}\n"
            formatted_text += f"Created: {self._replace_link(customer.get('created_at', 'Unknown'))}\n"
            formatted_text += f"Updated: {self._replace_link(customer.get('updated_at', 'Unknown'))}\n"
            formatted_text += f"Status: {self._replace_link(customer.get('state', 'Unknown'))}\n"
            formatted_text += f"Currency: {self._replace_link(customer.get('currency', 'Unknown'))}\n\n"
            formatted_text += "### Order Information\n"
            formatted_text += f"Orders Count: {customer.get('orders_count', 0)}\n"
            formatted_text += f"Total Spent: {customer.get('total_spent', '0.00')} {customer.get('currency', '')}\n"
            if customer.get('last_order_id'):
                formatted_text += f"Last Order ID: {customer.get('last_order_id')}\n"
                formatted_text += f"Last Order Name: {self._replace_link(customer.get('last_order_name', 'Unknown'))}\n\n"
            else:
                formatted_text += "No orders placed yet.\n\n"
            if customer.get('note'):
                formatted_text += "### Customer Note\n"
                formatted_text += f"{self._clean_html_description(customer.get('note'))}\n\n"
            if customer.get('addresses'):
                formatted_text += "### Addresses\n"
                for i, address in enumerate(customer.get('addresses', []), 1):
                    is_default = address.get('default', False)
                    formatted_text += f"Address {i}{' (Default)' if is_default else ''}:\n"
                    formatted_text += f"Name: {self._replace_link(address.get('name', ''))}\n"
                    if address.get('company'):
                        formatted_text += f"Company: {self._replace_link(address.get('company'))}\n"
                    formatted_text += f"Address: {self._replace_link(address.get('address1', ''))}\n"
                    if address.get('address2'):
                        formatted_text += f"Address 2: {self._replace_link(address.get('address2'))}\n"
                    formatted_text += f"City: {self._replace_link(address.get('city', ''))}\n"
                    formatted_text += f"Province/State: {self._replace_link(address.get('province', ''))}\n"
                    formatted_text += f"Country: {self._replace_link(address.get('country', ''))}\n"
                    formatted_text += f"Postal/ZIP Code: {self._replace_link(address.get('zip', ''))}\n"
                    formatted_text += f"Phone: {self._replace_link(address.get('phone', 'Not provided'))}\n\n"
            if customer.get('email_marketing_consent'):
                formatted_text += "### Email Marketing Consent\n"
                email_consent = customer.get('email_marketing_consent', {})
                formatted_text += f"State: {self._replace_link(email_consent.get('state', 'Unknown'))}\n"
                formatted_text += f"Opt-in Level: {self._replace_link(email_consent.get('opt_in_level', 'Unknown'))}\n"
                if email_consent.get('consent_updated_at'):
                    formatted_text += f"Updated: {self._replace_link(email_consent.get('consent_updated_at'))}\n"
                formatted_text += "\n"
            if customer.get('sms_marketing_consent'):
                formatted_text += "### SMS Marketing Consent\n"
                sms_consent = customer.get('sms_marketing_consent', {})
                formatted_text += f"State: {self._replace_link(sms_consent.get('state', 'Unknown'))}\n"
                formatted_text += f"Opt-in Level: {self._replace_link(sms_consent.get('opt_in_level', 'Unknown'))}\n"
                if sms_consent.get('consent_updated_at'):
                    formatted_text += f"Updated: {self._replace_link(sms_consent.get('consent_updated_at'))}\n"
                formatted_text += f"Collected From: {self._replace_link(sms_consent.get('consent_collected_from', 'Unknown'))}\n\n"
            if customer.get('tags'):
                formatted_text += "### Tags\n"
                formatted_text += f"{self._replace_link(customer.get('tags'))}\n\n"
            formatted_text += "\n---\n\n"
        return formatted_text

    def chat(self, message: str, conversation_id: Optional[str] = None) -> Tuple[str, str]:
        """Process a chat message and return a response using Pinecone Assistant."""
        # Create a new conversation if none exists
        if not conversation_id or conversation_id not in self.conversations:
            conversation_id = str(uuid.uuid4())
            self.conversations[conversation_id] = []

        # Add user message to conversation history
        self.conversations[conversation_id].append({"role": "user", "content": message})

        # Prepare messages for Pinecone Assistant API
        messages = []

        # Add conversation history (up to last 10 messages)
        for msg in self.conversations[conversation_id][-10:]:
            messages.append(Message(role=msg["role"], content=msg["content"]))

        # Get response from Pinecone Assistant
        try:
            response = self.assistant.chat(messages=messages)

            # Extract assistant's response
            assistant_response = response.message.content

            # Add assistant response to conversation history
            self.conversations[conversation_id].append({"role": "assistant", "content": assistant_response})

            return assistant_response, conversation_id
        except Exception as e:
            error_message = f"Error getting response from Pinecone Assistant: {str(e)}"
            return error_message, conversation_id

    def get_pinecone_usage_summary(self) -> Dict:
        """
        Get comprehensive usage summary for Pinecone Standard plan limits.

        Returns:
            Dict with current usage against all limits
        """
        try:
            # Get basic usage summary from limits manager
            usage_summary = self.limits_manager.get_usage_summary()

            # Add assistant-specific information
            assistant_info = {
                'assistant_name': self.assistant_name,
                'assistant_exists': self.assistant is not None,
                'shopify_connected': self.shopify_client is not None
            }

            # Get file information if assistant exists
            if self.assistant:
                try:
                    files = self.assistant.list_files()
                    file_details = []
                    total_files = len(files)

                    for file in files:
                        file_details.append({
                            'name': file.name,
                            'id': file.id,
                            'status': file.status,
                            'created_on': file.created_on,
                            'metadata': getattr(file, 'metadata', None)
                        })

                    assistant_info.update({
                        'total_files': total_files,
                        'file_details': file_details
                    })

                    # Update limits manager with actual file count
                    self.limits_manager.file_count = total_files

                except Exception as e:
                    logger.warning(f"Could not retrieve file information: {str(e)}")
                    assistant_info['file_error'] = str(e)

            # Combine all information
            complete_summary = {
                **usage_summary,
                'assistant_info': assistant_info,
                'recommendations': self._get_optimization_recommendations(usage_summary)
            }

            return complete_summary

        except Exception as e:
            logger.error(f"Failed to get usage summary: {str(e)}")
            return {
                'error': str(e),
                'plan_type': 'standard',
                'assistant_name': self.assistant_name
            }

    def _get_optimization_recommendations(self, usage_summary: Dict) -> List[str]:
        """
        Generate optimization recommendations based on current usage.

        Args:
            usage_summary: Current usage summary

        Returns:
            List of recommendation strings
        """
        recommendations = []

        # File count recommendations
        file_utilization = usage_summary.get('files', {}).get('utilization_percent', 0)
        if file_utilization > 80:
            recommendations.append("⚠️ File count is approaching limit (>80%). Consider consolidating data or removing old files.")
        elif file_utilization > 60:
            recommendations.append("📊 File count is moderate (>60%). Monitor file usage and plan for optimization.")

        # Storage recommendations
        storage_utilization = usage_summary.get('storage', {}).get('utilization_percent', 0)
        if storage_utilization > 80:
            recommendations.append("⚠️ Storage usage is approaching limit (>80%). Consider data compression or selective sync.")
        elif storage_utilization > 60:
            recommendations.append("📊 Storage usage is moderate (>60%). Monitor data size and optimize formatting.")

        # Token usage recommendations
        token_utilization = usage_summary.get('tokens', {}).get('utilization_percent', 0)
        if token_utilization > 80:
            recommendations.append("⚠️ Token usage is high (>80%). Consider implementing rate limiting or reducing query frequency.")
        elif token_utilization > 60:
            recommendations.append("📊 Token usage is moderate (>60%). Monitor API calls and optimize query patterns.")

        # General recommendations
        if not recommendations:
            recommendations.append("✅ All usage metrics are within healthy limits.")

        recommendations.extend([
            "💡 Use incremental sync to minimize data transfer",
            "💡 Implement file chunking for large datasets",
            "💡 Monitor usage regularly with get_pinecone_usage_summary()",
            "💡 Consider upgrading to Enterprise plan for higher limits if needed"
        ])

        return recommendations

    def upload_all_data_types(self, products_data, orders_data, customers_data):
        """
        Upload all data types (products, orders, customers) in chunks, update file IDs and hashes in DB.
        """
        if products_data:
            formatted_products = self._format_products_for_assistant(products_data)
            self._upload_data_in_chunks('products', products_data, formatted_products, 'products_catalog')
        if orders_data:
            formatted_orders = self._format_orders_for_assistant(orders_data)
            self._upload_data_in_chunks('orders', orders_data, formatted_orders, 'orders_data')
        if customers_data:
            formatted_customers = self._format_customers_for_assistant(customers_data)
            self._upload_data_in_chunks('customers', customers_data, formatted_customers, 'customers_data')

    def sync_all_data_types(self, products_data, orders_data, customers_data):
        """
        Sync all data types (products, orders, customers):
        - Calculate hash for each data type
        - Compare with stored hash in DB
        - Only upload if hash differs
        """
        from database.crud import AssistantCRUD
        from database.session import SessionLocal
        from database.models import calculate_data_hash
        db = SessionLocal()
        try:
            assistant_record = AssistantCRUD.get_assistant_by_name(db, self.assistant_name)
            if not assistant_record:
                print("Assistant record not found in DB.")
                return
            # Products
            do_products = False
            if products_data:
                new_products_hash = calculate_data_hash(products_data)
                if assistant_record.products_hash != new_products_hash:
                    do_products = True
            # Orders
            do_orders = False
            if orders_data:
                new_orders_hash = calculate_data_hash(orders_data)
                if assistant_record.orders_hash != new_orders_hash:
                    do_orders = True
            # Customers
            do_customers = False
            if customers_data:
                new_customers_hash = calculate_data_hash(customers_data)
                if assistant_record.customers_hash != new_customers_hash:
                    do_customers = True
            # Upload only changed data types
            if do_products or do_orders or do_customers:
                if do_products:
                    formatted_products = self._format_products_for_assistant(products_data)
                    self._upload_data_in_chunks('products', products_data, formatted_products, 'products_catalog')
                if do_orders:
                    formatted_orders = self._format_orders_for_assistant(orders_data)
                    self._upload_data_in_chunks('orders', orders_data, formatted_orders, 'orders_data')
                if do_customers:
                    formatted_customers = self._format_customers_for_assistant(customers_data)
                    self._upload_data_in_chunks('customers', customers_data, formatted_customers, 'customers_data')
            else:
                print("No changes detected in any data type. Skipping upload.")
        finally:
            db.close()
