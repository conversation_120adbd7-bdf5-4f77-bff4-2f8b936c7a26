// Store management functionality

let stores = [];
let connectionTestPassed = false;

// Initialize stores page
function initStoresPage() {
    // Require authentication
    if (!requireAuth()) return;

    // Load stores
    loadStores();

    // Setup event listeners
    setupEventListeners();

    // Check for URL parameters (OAuth callback messages)
    checkUrlParams();
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');

    // Add store button (always exists)
    const addStoreBtn = document.getElementById('add-store-btn');
    if (addStoreBtn) {
        addStoreBtn.addEventListener('click', showAddStoreForm);
        console.log('Add store button listener attached');
    }

    // Form elements (only exist when form is visible)
    setupFormEventListeners();
}

// Setup form event listeners (called when form is shown)
function setupFormEventListeners() {
    console.log('Setting up form event listeners...');

    // Cancel button
    const cancelBtn = document.getElementById('cancel-btn');

    if (cancelBtn) {
        cancelBtn.removeEventListener('click', hideAddStoreForm); // Remove existing listener
        cancelBtn.addEventListener('click', hideAddStoreForm);
        console.log('Cancel button listener attached');
    }

    // Test connection button
    const testBtn = document.getElementById('test-connection-btn');
    if (testBtn) {
        testBtn.removeEventListener('click', testConnection); // Remove existing listener
        testBtn.addEventListener('click', testConnection);
        console.log('Test connection button listener attached');
    }

    // Manual form submission
    const manualForm = document.getElementById('manual-store-form');

    console.log('Manual form element:', manualForm);

    if (manualForm) {
        manualForm.removeEventListener('submit', handleStoreSubmit); // Remove existing listener
        manualForm.addEventListener('submit', handleStoreSubmit);
        console.log('Manual form event listener attached');
    } else {
        console.error('Manual form not found!');
    }

    // Input change listeners to reset connection test status
    const shopUrlInput = document.getElementById('shop-url');
    const accessTokenInput = document.getElementById('access-token');

    if (shopUrlInput) {
        shopUrlInput.removeEventListener('input', resetConnectionTest);
        shopUrlInput.addEventListener('input', resetConnectionTest);
    }

    if (accessTokenInput) {
        accessTokenInput.removeEventListener('input', resetConnectionTest);
        accessTokenInput.addEventListener('input', resetConnectionTest);
    }
}

// Reset connection test status when inputs change
function resetConnectionTest() {
    connectionTestPassed = false;
    const saveBtn = document.getElementById('save-store-btn');
    if (saveBtn) {
        saveBtn.disabled = true;
        // Apply disabled styling
        saveBtn.style.backgroundColor = '#6c757d';
        saveBtn.style.color = '#fff';
        saveBtn.style.cursor = 'not-allowed';
        saveBtn.style.opacity = '0.6';
    }
    hideMessages();
}

// Check URL parameters for messages
function checkUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.has('store_connected')) {
        showSuccess('Store connected successfully!');
        // Remove URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
        // Reload stores
        setTimeout(loadStores, 1000);
    }

    if (urlParams.has('error')) {
        showError(urlParams.get('error'));
        // Remove URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
    }
}

// Load stores from API
async function loadStores() {
    try {
        const response = await fetch('/stores/?include_stats=true', {
            headers: getAuthHeaders()
        });

        if (response.ok) {
            stores = await response.json();
            renderStores();
        } else {
            showError('Failed to load stores');
        }
    } catch (error) {
        showError('Network error while loading stores');
    }
}

// Render stores in the grid
function renderStores() {
    const storesGrid = document.getElementById('stores-grid');
    const emptyState = document.getElementById('empty-state');

    if (stores.length === 0) {
        storesGrid.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }

    storesGrid.style.display = 'grid';
    emptyState.style.display = 'none';

    storesGrid.innerHTML = stores.map(store => createStoreCard(store)).join('');

    // Add event listeners to store action buttons
    addStoreActionListeners();
}

// Create store card HTML
function createStoreCard(store) {
    const stats = store.stats || {};
    const statusClass = `status-${store.status}`;

    return `
        <div class="store-card" data-store-id="${store.id}">
            <div class="store-header">
                <div class="store-name">${store.store_name || store.shop_url}</div>
                <div class="store-url">${store.shop_url}</div>
                <span class="store-status ${statusClass}">${store.status}</span>
            </div>

            <div class="store-stats">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_products || 0}</div>
                        <div class="stat-label">Products</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_orders || 0}</div>
                        <div class="stat-label">Orders</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_customers || 0}</div>
                        <div class="stat-label">Customers</div>
                    </div>
                </div>
            </div>

            <div class="store-actions">
                <button onclick="syncStore(${store.id})" class="btn btn-primary btn-sm">
                    Sync
                </button>
                <button onclick="viewStore(${store.id})" class="btn btn-secondary btn-sm">
                    View
                </button>
                <button onclick="deleteStore(${store.id})" class="btn btn-danger btn-sm">
                    Delete
                </button>
            </div>
        </div>
    `;
}

// Add event listeners to store action buttons
function addStoreActionListeners() {
    // Event listeners are added via onclick in the HTML for simplicity
    // In a production app, you might want to use event delegation
}

// Show add store form
function showAddStoreForm() {
    document.getElementById('add-store-form').style.display = 'block';

    // Reset form state
    connectionTestPassed = false;

    // Ensure save button is disabled
    const saveBtn = document.getElementById('save-store-btn');
    if (saveBtn) {
        saveBtn.disabled = true;
        // Force visual update
        saveBtn.style.backgroundColor = '#6c757d';
        saveBtn.style.color = '#fff';
        saveBtn.style.cursor = 'not-allowed';
        saveBtn.style.opacity = '0.6';
    }

    // Setup form event listeners now that the form is visible
    setupFormEventListeners();

    // Clear form and focus on first input
    document.getElementById('manual-store-form').reset();
    document.getElementById('shop-url').focus();

    hideMessages();
}

// Hide add store form
function hideAddStoreForm() {
    document.getElementById('add-store-form').style.display = 'none';
    // Reset form
    document.getElementById('manual-store-form').reset();
    connectionTestPassed = false;
    if (document.getElementById('save-store-btn')) {
        document.getElementById('save-store-btn').disabled = true;
    }
    hideMessages();
}

// Test store connection
async function testConnection() {
    const shopUrl = document.getElementById('shop-url').value.trim();
    const accessToken = document.getElementById('access-token').value.trim();

    if (!shopUrl || !accessToken) {
        showError('Please enter both shop URL and access token');
        return;
    }

    const loading = document.getElementById('form-loading');
    const testBtn = document.getElementById('test-connection-btn');

    loading.style.display = 'block';
    testBtn.disabled = true;
    hideMessages();

    try {
        const response = await fetch('/stores/test-connection', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({
                shop_url: shopUrl,
                access_token: accessToken
            })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('Connection test successful! You can now save the store.');
            connectionTestPassed = true;
            const saveBtn = document.getElementById('save-store-btn');
            if (saveBtn) {
                saveBtn.disabled = false;
                // Restore original button styling
                saveBtn.style.backgroundColor = '';
                saveBtn.style.color = '';
                saveBtn.style.cursor = '';
                saveBtn.style.opacity = '';
            }
        } else {
            showError(`Connection test failed: ${result.message}`);
            connectionTestPassed = false;
            const saveBtn = document.getElementById('save-store-btn');
            if (saveBtn) {
                saveBtn.disabled = true;
                // Apply disabled styling
                saveBtn.style.backgroundColor = '#6c757d';
                saveBtn.style.color = '#fff';
                saveBtn.style.cursor = 'not-allowed';
                saveBtn.style.opacity = '0.6';
            }
        }
    } catch (error) {
        showError('Network error during connection test');
        connectionTestPassed = false;
        const saveBtn = document.getElementById('save-store-btn');
        if (saveBtn) {
            saveBtn.disabled = true;
            // Apply disabled styling
            saveBtn.style.backgroundColor = '#6c757d';
            saveBtn.style.color = '#fff';
            saveBtn.style.cursor = 'not-allowed';
            saveBtn.style.opacity = '0.6';
        }
    } finally {
        loading.style.display = 'none';
        testBtn.disabled = false;
    }
}

// Handle manual store form submission
async function handleStoreSubmit(e) {
    e.preventDefault();

    if (!connectionTestPassed) {
        showError('Please test the connection first');
        return;
    }

    const shopUrl = document.getElementById('shop-url').value.trim();
    const accessToken = document.getElementById('access-token').value.trim();

    const loading = document.getElementById('form-loading');
    const saveBtn = document.getElementById('save-store-btn');

    loading.style.display = 'block';
    saveBtn.disabled = true;
    hideMessages();

    try {
        const response = await fetch('/stores/manual', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({
                shop_url: shopUrl,
                access_token: accessToken
            })
        });

        if (response.ok) {
            showSuccess('Store added successfully!');
            hideAddStoreForm();
            loadStores(); // Reload stores
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to add store');
        }
    } catch (error) {
        showError('Network error while adding store');
    } finally {
        loading.style.display = 'none';
        saveBtn.disabled = false;
    }
}

// Sync store data
async function syncStore(storeId, forceSync = false) {
    let confirmMessage = 'Sync store data from Shopify?';
    if (forceSync) {
        confirmMessage = 'Force sync store data from Shopify? This will refresh all data even if no changes are detected.';
    }

    if (!confirm(confirmMessage)) return;

    try {
        const requestBody = forceSync ? { force_sync: true } : {};

        const response = await fetch(`/stores/${storeId}/sync`, {
            method: 'POST',
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (response.ok) {
            const result = await response.json();
            showSuccess(result.message || 'Store sync completed successfully');
            loadStores(); // Reload stores to show updated data
        } else {
            const error = await response.json();
            showError(error.detail || 'Store sync failed');
        }
    } catch (error) {
        showError('Network error during store sync');
    }
}

// Force sync store data
async function forceSyncStore(storeId) {
    await syncStore(storeId, true);
}

// View store details
function viewStore(storeId) {
    // Show basic store information
    const store = stores.find(s => s.id === storeId);
    if (store) {
        alert(`Store: ${store.store_name || store.shop_url}\nStatus: ${store.status}\nURL: ${store.shop_url}`);
    }
}

// Delete store
async function deleteStore(storeId) {
    const store = stores.find(s => s.id === storeId);
    const storeName = store ? (store.store_name || store.shop_url) : 'this store';

    if (!confirm(`Are you sure you want to delete ${storeName}? This action cannot be undone.`)) {
        return;
    }

    try {
        const response = await fetch(`/stores/${storeId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            showSuccess('Store deleted successfully');
            loadStores(); // Reload stores
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to delete store');
        }
    } catch (error) {
        showError('Network error while deleting store');
    }
}

// Show error message
function showError(message) {
    const errorDiv = document.getElementById('error-message');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';

    // Hide success message
    document.getElementById('success-message').style.display = 'none';

    // Auto-hide after 5 seconds
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

// Show success message
function showSuccess(message) {
    const successDiv = document.getElementById('success-message');
    successDiv.textContent = message;
    successDiv.style.display = 'block';

    // Hide error message
    document.getElementById('error-message').style.display = 'none';

    // Auto-hide after 3 seconds
    setTimeout(() => {
        successDiv.style.display = 'none';
    }, 3000);
}

// Hide all messages
function hideMessages() {
    document.getElementById('error-message').style.display = 'none';
    document.getElementById('success-message').style.display = 'none';
}

// Make functions available globally for HTML onclick handlers
window.syncStore = syncStore;
window.forceSyncStore = forceSyncStore;
window.viewStore = viewStore;
window.deleteStore = deleteStore;
window.showAddStoreForm = showAddStoreForm;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname === '/stores') {
        initStoresPage();
    }
});
