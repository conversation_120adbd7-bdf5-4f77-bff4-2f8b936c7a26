document.addEventListener('DOMContentLoaded', function() {
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');
    const chatButton = document.getElementById('chat-button');
    const closeButton = document.getElementById('close-chat');
    const chatWidget = document.getElementById('chat-widget');

    let conversationId = null;

    // Function to toggle chat widget visibility
    function toggleChat() {
        chatWidget.classList.toggle('active');

        // If opening the chat, focus on the input field
        if (chatWidget.classList.contains('active')) {
            // Hide the chat button when chat is open
            chatButton.style.display = 'none';
            // Focus on input after a short delay to ensure the animation is complete
            setTimeout(() => {
                userInput.focus();
            }, 300);
        } else {
            // Show the chat button when chat is closed
            chatButton.style.display = 'flex';
        }
    }

    // Function to add a message to the chat
    function addMessage(message, isUser) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        // Process message text (handle markdown-like formatting)
        let formattedMessage = message;

        // Convert bullet points (* or - followed by space)
        formattedMessage = formattedMessage.replace(/^[*-] (.+)$/gm, '<li>$1</li>');
        if (formattedMessage.includes('<li>')) {
            formattedMessage = '<ul>' + formattedMessage + '</ul>';
        }

        // Convert headers (# followed by space)
        formattedMessage = formattedMessage.replace(/^# (.+)$/gm, '<h3>$1</h3>');
        formattedMessage = formattedMessage.replace(/^## (.+)$/gm, '<h4>$1</h4>');

        // Convert bold (**text**)
        formattedMessage = formattedMessage.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');

        // Convert italic (*text*)
        formattedMessage = formattedMessage.replace(/\*(.+?)\*/g, '<em>$1</em>');

        // Convert line breaks
        formattedMessage = formattedMessage.replace(/\n/g, '<br>');

        messageContent.innerHTML = `<p>${formattedMessage}</p>`;
        messageDiv.appendChild(messageContent);
        chatMessages.appendChild(messageDiv);

        // Scroll to the bottom of the chat
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Function to send a message to the API
    async function sendMessage(message) {
        // Add user message to the chat
        addMessage(message, true);

        // Show loading indicator
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'message assistant';
        loadingDiv.innerHTML = '<div class="message-content"><p>Thinking...</p></div>';
        chatMessages.appendChild(loadingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    conversation_id: conversationId
                })
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();

            // Remove loading indicator
            chatMessages.removeChild(loadingDiv);

            // Add assistant response to the chat
            addMessage(data.response, false);

            // Update conversation ID
            conversationId = data.conversation_id;
        } catch (error) {
            // Remove loading indicator
            chatMessages.removeChild(loadingDiv);

            // Add error message
            addMessage('Sorry, there was an error processing your request. Please try again.', false);
            console.error('Error:', error);
        }
    }

    // Event listener for chat button
    chatButton.addEventListener('click', toggleChat);

    // Event listener for close button
    closeButton.addEventListener('click', toggleChat);

    // Event listener for send button
    sendButton.addEventListener('click', function() {
        const message = userInput.value.trim();
        if (message) {
            sendMessage(message);
            userInput.value = '';
        }
    });

    // Event listener for Enter key
    userInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const message = userInput.value.trim();
            if (message) {
                sendMessage(message);
                userInput.value = '';
            }
        }
    });

    // Initially hide the chat widget
    chatWidget.classList.remove('active');
});
