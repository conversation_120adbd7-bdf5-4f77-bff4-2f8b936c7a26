// Authentication utilities and handlers

// Check if user is authenticated
function isAuthenticated() {
    const token = localStorage.getItem('access_token');
    return token !== null;
}

// Get stored user data
function getUserData() {
    const userData = localStorage.getItem('user_data');
    return userData ? JSON.parse(userData) : null;
}

// Get authorization headers for API calls
function getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// Logout user
function logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user_data');
    window.location.href = '/login';
}

// Validate token with server
async function validateToken() {
    const token = localStorage.getItem('access_token');
    if (!token) return false;
    
    try {
        const response = await fetch('/auth/validate-token', {
            headers: getAuthHeaders()
        });
        
        if (!response.ok) {
            logout();
            return false;
        }
        
        return true;
    } catch (error) {
        console.error('Token validation error:', error);
        logout();
        return false;
    }
}

// Redirect to login if not authenticated
function requireAuth() {
    if (!isAuthenticated()) {
        window.location.href = '/login';
        return false;
    }
    return true;
}

// Redirect to dashboard if already authenticated
function redirectIfAuthenticated() {
    if (isAuthenticated()) {
        window.location.href = '/dashboard';
        return true;
    }
    return false;
}

// Handle login form submission
async function handleLogin(username, password) {
    try {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            // Store token and user data
            localStorage.setItem('access_token', data.access_token);
            localStorage.setItem('user_data', JSON.stringify(data.user));
            return { success: true, data: data };
        } else {
            return { success: false, error: data.detail || 'Login failed' };
        }
    } catch (error) {
        return { success: false, error: 'Network error. Please try again.' };
    }
}

// Handle registration form submission
async function handleRegister(username, email, password) {
    try {
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                email: email,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            // Store token and user data
            localStorage.setItem('access_token', data.access_token);
            localStorage.setItem('user_data', JSON.stringify(data.user));
            return { success: true, data: data };
        } else {
            return { success: false, error: data.detail || 'Registration failed' };
        }
    } catch (error) {
        return { success: false, error: 'Network error. Please try again.' };
    }
}

// Show/hide loading state
function setLoadingState(isLoading, buttonId, loadingId) {
    const button = document.getElementById(buttonId);
    const loading = document.getElementById(loadingId);
    
    if (button) button.disabled = isLoading;
    if (loading) loading.style.display = isLoading ? 'block' : 'none';
}

// Show error message
function showError(message, elementId = 'error-message') {
    const errorDiv = document.getElementById(elementId);
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }
}

// Show success message
function showSuccess(message, elementId = 'success-message') {
    const successDiv = document.getElementById(elementId);
    if (successDiv) {
        successDiv.textContent = message;
        successDiv.style.display = 'block';
    }
}

// Hide all messages
function hideMessages() {
    const errorDiv = document.getElementById('error-message');
    const successDiv = document.getElementById('success-message');
    
    if (errorDiv) errorDiv.style.display = 'none';
    if (successDiv) successDiv.style.display = 'none';
}

// Initialize login page
function initLoginPage() {
    // Redirect if already authenticated
    if (redirectIfAuthenticated()) return;
    
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            hideMessages();
            setLoadingState(true, 'login-btn', 'loading');
            
            const result = await handleLogin(username, password);
            
            if (result.success) {
                showSuccess('Login successful! Redirecting...');
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
            } else {
                showError(result.error);
            }
            
            setLoadingState(false, 'login-btn', 'loading');
        });
    }
}

// Initialize register page
function initRegisterPage() {
    // Redirect if already authenticated
    if (redirectIfAuthenticated()) return;
    
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            hideMessages();
            
            // Check if passwords match
            if (password !== confirmPassword) {
                showError('Passwords do not match');
                return;
            }
            
            setLoadingState(true, 'register-btn', 'loading');
            
            const result = await handleRegister(username, email, password);
            
            if (result.success) {
                showSuccess('Registration successful! Redirecting...');
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
            } else {
                if (typeof result.error === 'object' && result.error.suggestions) {
                    showError(`${result.error.message}. Suggestions: ${result.error.suggestions.join(', ')}`);
                } else {
                    showError(result.error);
                }
            }
            
            setLoadingState(false, 'register-btn', 'loading');
        });
    }
}

// Initialize dashboard page
function initDashboardPage() {
    // Require authentication
    if (!requireAuth()) return;
    
    // Validate token on page load
    validateToken();
    
    // Display user info
    const userData = getUserData();
    if (userData) {
        const userNameElement = document.getElementById('user-name');
        const userEmailElement = document.getElementById('user-email');
        
        if (userNameElement) userNameElement.textContent = userData.username;
        if (userEmailElement) userEmailElement.textContent = userData.email;
    }
    
    // Setup logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }
}

// Auto-initialize based on current page
document.addEventListener('DOMContentLoaded', function() {
    const path = window.location.pathname;
    
    if (path === '/login') {
        initLoginPage();
    } else if (path === '/register') {
        initRegisterPage();
    } else if (path === '/dashboard') {
        initDashboardPage();
    }
});
