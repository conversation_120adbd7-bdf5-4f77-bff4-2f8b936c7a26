:root {
    --primary-color: #4a90e2;
    --secondary-color: #f5f5f5;
    --accent-color: #ff6b6b;
    --text-color: #333;
    --light-text: #666;
    --border-color: #ddd;
    --assistant-bg: #f1f1f1;
    --user-bg: #4a90e2;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f9f9f9;
    padding: 20px;
    position: relative;
    min-height: 100vh;
}

.site-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Chat Toggle Button */
.chat-toggle-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 999;
    transition: all 0.3s ease;
}

.chat-toggle-button:hover {
    background-color: #3a7bc8;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.chat-toggle-button i {
    font-size: 24px;
}

.chat-toggle-button span {
    display: none;
}

/* Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 380px;
    height: 450px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
}

.chat-widget.active {
    transform: translateX(0);
    opacity: 1;
}

.chat-header {
    background-color: var(--primary-color);
    color: white;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.chat-header h2 {
    font-size: 16px;
    margin: 0;
    font-weight: 500;
}

.close-button {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: opacity 0.3s;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    opacity: 0.8;
}

.chat-container {
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 15px;
    scrollbar-width: thin;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background-color: #d1d1d1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}

.message {
    display: flex;
    flex-direction: column;
}

.message.user {
    align-items: flex-end;
}

.message.assistant {
    align-items: flex-start;
}

.message-content {
    max-width: 85%;
    padding: 12px 15px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.user .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: 5px;
}

.assistant .message-content {
    background-color: #f1f1f1;
    color: var(--text-color);
    border-bottom-left-radius: 5px;
}

.message-content p {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.chat-input-container {
    display: flex;
    border-top: 1px solid #eaeaea;
    overflow: hidden;
    background-color: white;
    padding: 10px 15px;
    align-items: center;
}

#user-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    outline: none;
    font-size: 14px;
    margin-right: 10px;
    color: #333;
}

#send-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

#send-button i {
    font-size: 14px;
}

#send-button:hover {
    background-color: #3a7bc8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .site-content {
        padding: 10px;
    }

    .chat-widget {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
    }

    .chat-header {
        border-radius: 0;
    }

    .chat-toggle-button {
        bottom: 20px;
        right: 20px;
    }

    .message-content {
        max-width: 90%;
    }
}
