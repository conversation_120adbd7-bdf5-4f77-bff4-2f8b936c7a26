"""
Database Session Management Module

Handles database connections, session management, and configuration
for the Pinecone E-commerce Assistant application.
"""

import os
import logging
from urllib.parse import quote_plus
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy.exc import SQLAlchemyError
from dotenv import load_dotenv

from .models import Base

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """Database configuration class"""

    def __init__(self):
        # MySQL connection parameters
        self.db_user = os.getenv("DB_USER", "root")
        self.db_password = os.getenv("DB_PASSWORD", "")
        self.db_host = os.getenv("DB_HOST", "localhost")
        self.db_port = os.getenv("DB_PORT", "3306")
        self.db_name = os.getenv("DB_NAME", "pinecone_ecommerce")

        # Encrypt password for URL safety
        if self.db_password:
            self.db_password_encoded = quote_plus(self.db_password)
        else:
            self.db_password_encoded = ""

    def get_database_url(self):
        """Generate MySQL database URL"""
        if self.db_password_encoded:
            return f"mysql+pymysql://{self.db_user}:{self.db_password_encoded}@{self.db_host}:{self.db_port}/{self.db_name}"
        else:
            return f"mysql+pymysql://{self.db_user}@{self.db_host}:{self.db_port}/{self.db_name}"

# Initialize database configuration
db_config = DatabaseConfig()

# Create the SQLAlchemy engine
engine = create_engine(
    db_config.get_database_url(),
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False  # Set to True for SQL debugging
)

# Configure the session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """
    Dependency to get database session with proper error handling

    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    except SQLAlchemyError as e:
        logger.error(f"Database session error: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def init_db():
    """
    Initialize database tables

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully!")
        return True
    except SQLAlchemyError as e:
        logger.error(f"Error creating database tables: {str(e)}")
        return False

def test_connection():
    """
    Test database connection

    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
            logger.info("Database connection successful!")
            return True
    except SQLAlchemyError as e:
        logger.error(f"Database connection failed: {str(e)}")
        return False
