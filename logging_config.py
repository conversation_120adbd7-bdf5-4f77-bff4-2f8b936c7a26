"""
Production-grade logging configuration for Pinecone E-commerce Assistant

This module provides comprehensive logging setup with:
- File rotation and retention
- Different log levels for different components
- Structured logging with JSON format
- Performance monitoring
- Error tracking
- Audit trails
"""

import logging
import logging.handlers
import os
import sys
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from pathlib import Path

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'extra_data'):
            log_entry['extra'] = record.extra_data
            
        # Add user context if available
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
            
        # Add store context if available
        if hasattr(record, 'store_id'):
            log_entry['store_id'] = record.store_id
            
        # Add request context if available
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
            
        return json.dumps(log_entry, default=str)

class ProductionLogger:
    """Production-grade logger configuration"""
    
    def __init__(self, log_dir: str = "logs"):
        """Initialize production logger"""
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for different log types
        (self.log_dir / "app").mkdir(exist_ok=True)
        (self.log_dir / "sync").mkdir(exist_ok=True)
        (self.log_dir / "api").mkdir(exist_ok=True)
        (self.log_dir / "auth").mkdir(exist_ok=True)
        (self.log_dir / "errors").mkdir(exist_ok=True)
        (self.log_dir / "audit").mkdir(exist_ok=True)
        
        self._setup_loggers()
    
    def _setup_loggers(self):
        """Setup all loggers with appropriate handlers"""
        
        # Root logger configuration
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler for development
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # Main application log
        self._setup_file_logger(
            logger_name="app",
            filename="app/application.log",
            level=logging.INFO,
            max_bytes=50*1024*1024,  # 50MB
            backup_count=10
        )
        
        # Sync operations log
        self._setup_file_logger(
            logger_name="sync",
            filename="sync/sync_operations.log",
            level=logging.INFO,
            max_bytes=100*1024*1024,  # 100MB
            backup_count=15
        )
        
        # API requests log
        self._setup_file_logger(
            logger_name="api",
            filename="api/api_requests.log",
            level=logging.INFO,
            max_bytes=50*1024*1024,  # 50MB
            backup_count=10
        )
        
        # Authentication log
        self._setup_file_logger(
            logger_name="auth",
            filename="auth/authentication.log",
            level=logging.INFO,
            max_bytes=20*1024*1024,  # 20MB
            backup_count=5
        )
        
        # Error log
        self._setup_file_logger(
            logger_name="error",
            filename="errors/errors.log",
            level=logging.ERROR,
            max_bytes=50*1024*1024,  # 50MB
            backup_count=20
        )
        
        # Audit log
        self._setup_file_logger(
            logger_name="audit",
            filename="audit/audit_trail.log",
            level=logging.INFO,
            max_bytes=100*1024*1024,  # 100MB
            backup_count=30  # Keep longer for compliance
        )
        
        # Scheduler log
        self._setup_file_logger(
            logger_name="scheduler",
            filename="sync/scheduler.log",
            level=logging.INFO,
            max_bytes=30*1024*1024,  # 30MB
            backup_count=10
        )
    
    def _setup_file_logger(self, logger_name: str, filename: str, level: int, 
                          max_bytes: int, backup_count: int):
        """Setup a file logger with rotation"""
        
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)
        
        # Prevent duplicate handlers
        if logger.handlers:
            return
        
        # File handler with rotation
        file_path = self.log_dir / filename
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        
        # Use JSON formatter for structured logging
        json_formatter = JSONFormatter()
        file_handler.setFormatter(json_formatter)
        
        logger.addHandler(file_handler)
        logger.propagate = False  # Prevent duplicate console output

class LoggerMixin:
    """Mixin class to add logging capabilities to any class"""
    
    @property
    def logger(self):
        """Get logger for the current class"""
        return logging.getLogger(self.__class__.__module__)
    
    def log_info(self, message: str, **kwargs):
        """Log info message with context"""
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def log_warning(self, message: str, **kwargs):
        """Log warning message with context"""
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """Log error message with context"""
        self._log_with_context(logging.ERROR, message, **kwargs)
        # Also log to error logger
        error_logger = logging.getLogger('error')
        self._log_with_context_to_logger(error_logger, logging.ERROR, message, **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """Log debug message with context"""
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """Log message with additional context"""
        self._log_with_context_to_logger(self.logger, level, message, **kwargs)
    
    def _log_with_context_to_logger(self, logger: logging.Logger, level: int, 
                                   message: str, **kwargs):
        """Log message to specific logger with context"""
        extra = {}
        
        # Extract known context fields
        if 'user_id' in kwargs:
            extra['user_id'] = kwargs.pop('user_id')
        if 'store_id' in kwargs:
            extra['store_id'] = kwargs.pop('store_id')
        if 'request_id' in kwargs:
            extra['request_id'] = kwargs.pop('request_id')
        
        # Add remaining kwargs as extra data
        if kwargs:
            extra['extra_data'] = kwargs
        
        logger.log(level, message, extra=extra)

def setup_production_logging(log_dir: str = "logs") -> ProductionLogger:
    """Setup production logging configuration"""
    return ProductionLogger(log_dir)

def get_logger(name: str) -> logging.Logger:
    """Get a logger by name"""
    return logging.getLogger(name)

def log_api_request(method: str, path: str, status_code: int, 
                   duration: float, user_id: Optional[int] = None,
                   request_id: Optional[str] = None):
    """Log API request details"""
    api_logger = logging.getLogger('api')
    
    extra = {
        'user_id': user_id,
        'request_id': request_id,
        'extra_data': {
            'method': method,
            'path': path,
            'status_code': status_code,
            'duration_ms': round(duration * 1000, 2)
        }
    }
    
    api_logger.info(f"{method} {path} - {status_code} ({duration:.3f}s)", extra=extra)

def log_auth_event(event_type: str, user_id: Optional[int] = None, 
                  email: Optional[str] = None, success: bool = True,
                  details: Optional[Dict[str, Any]] = None):
    """Log authentication events"""
    auth_logger = logging.getLogger('auth')
    
    extra = {
        'user_id': user_id,
        'extra_data': {
            'event_type': event_type,
            'email': email,
            'success': success,
            'details': details or {}
        }
    }
    
    level = logging.INFO if success else logging.WARNING
    message = f"Auth event: {event_type} - {'Success' if success else 'Failed'}"
    if email:
        message += f" (User: {email})"
    
    auth_logger.log(level, message, extra=extra)

def log_audit_event(action: str, resource_type: str, resource_id: Optional[int] = None,
                   user_id: Optional[int] = None, details: Optional[Dict[str, Any]] = None):
    """Log audit trail events"""
    audit_logger = logging.getLogger('audit')
    
    extra = {
        'user_id': user_id,
        'extra_data': {
            'action': action,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'details': details or {}
        }
    }
    
    message = f"Audit: {action} {resource_type}"
    if resource_id:
        message += f" (ID: {resource_id})"
    
    audit_logger.info(message, extra=extra)

# Global production logger instance
production_logger = None

def init_logging(log_dir: str = "logs"):
    """Initialize production logging"""
    global production_logger
    production_logger = setup_production_logging(log_dir)
    return production_logger
