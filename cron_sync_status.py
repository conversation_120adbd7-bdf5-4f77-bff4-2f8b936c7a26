"""
Cron Sync Status Management

This module provides functionality to read and manage the status
of cron-based auto-sync operations.
"""

import json
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger('cron_sync_status')

def get_cron_sync_status() -> Dict[str, Any]:
    """
    Get the current status of cron-based auto-sync operations
    
    Returns:
        Dict containing sync status information
    """
    try:
        project_root = Path(__file__).parent
        status_file = project_root / 'logs' / 'sync' / 'cron_status.json'
        
        if not status_file.exists():
            return {
                'status': 'no_data',
                'message': 'No cron sync data available yet',
                'cron_schedule': '0 0,12 * * * (Every 12 hours at 12:00 AM and 12:00 PM UTC)',
                'next_sync_times': [
                    '00:00:00 UTC (Daily)',
                    '12:00:00 UTC (Daily)'
                ]
            }
        
        # Read status file
        with open(status_file, 'r') as f:
            status_data = json.load(f)
        
        # Add additional computed information
        status_data['cron_schedule'] = '0 0,12 * * * (Every 12 hours at 12:00 AM and 12:00 PM UTC)'
        status_data['file_last_modified'] = datetime.fromtimestamp(
            status_file.stat().st_mtime, tz=timezone.utc
        ).isoformat()
        
        # Calculate time since last sync
        if 'last_sync_time' in status_data:
            last_sync = datetime.fromisoformat(status_data['last_sync_time'])
            time_since_sync = (datetime.now(timezone.utc) - last_sync).total_seconds()
            status_data['time_since_last_sync_seconds'] = time_since_sync
            status_data['time_since_last_sync_hours'] = time_since_sync / 3600
        
        return status_data
        
    except Exception as e:
        logger.error(f"Error reading cron sync status: {str(e)}")
        return {
            'status': 'error',
            'error': str(e),
            'message': 'Failed to read cron sync status'
        }

def update_cron_sync_status(status_data: Dict[str, Any]) -> bool:
    """
    Update the cron sync status file
    
    Args:
        status_data: Status data to save
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        project_root = Path(__file__).parent
        status_file = project_root / 'logs' / 'sync' / 'cron_status.json'
        status_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Add timestamp
        status_data['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        with open(status_file, 'w') as f:
            json.dump(status_data, f, indent=2)
        
        logger.info(f"Cron sync status updated: {status_file}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to update cron sync status: {str(e)}")
        return False

def get_cron_schedule_info() -> Dict[str, Any]:
    """
    Get information about the cron schedule
    
    Returns:
        Dict containing cron schedule information
    """
    return {
        'cron_expression': '0 0,12 * * *',
        'description': 'Every 12 hours at 12:00 AM and 12:00 PM UTC',
        'timezone': 'UTC',
        'frequency': 'Every 12 hours',
        'next_runs': [
            '00:00:00 UTC (Daily)',
            '12:00:00 UTC (Daily)'
        ],
        'setup_instructions': {
            'command': 'crontab -e',
            'entry': '0 0,12 * * * cd /path/to/your/app && python auto_sync_cron.py >> /var/log/auto_sync.log 2>&1',
            'log_file': '/var/log/auto_sync.log'
        }
    }

def check_cron_health() -> Dict[str, Any]:
    """
    Check the health of cron-based sync operations
    
    Returns:
        Dict containing health check results
    """
    try:
        status = get_cron_sync_status()
        
        if status.get('status') == 'no_data':
            return {
                'health': 'unknown',
                'message': 'No sync data available - cron may not be set up yet',
                'recommendations': [
                    'Set up cron job using setup_cron.py',
                    'Verify cron service is running',
                    'Check cron logs for errors'
                ]
            }
        
        if status.get('status') == 'error':
            return {
                'health': 'unhealthy',
                'message': 'Error reading sync status',
                'error': status.get('error'),
                'recommendations': [
                    'Check file permissions',
                    'Verify log directory exists',
                    'Check application logs'
                ]
            }
        
        # Check if last sync was recent (within 13 hours to account for some delay)
        if 'time_since_last_sync_hours' in status:
            hours_since_sync = status['time_since_last_sync_hours']
            
            if hours_since_sync > 13:  # More than 13 hours since last sync
                return {
                    'health': 'warning',
                    'message': f'Last sync was {hours_since_sync:.1f} hours ago (expected every 12 hours)',
                    'last_sync': status.get('last_sync_time'),
                    'recommendations': [
                        'Check if cron job is running',
                        'Verify cron service status',
                        'Check system logs for cron errors',
                        'Manually run auto_sync_cron.py to test'
                    ]
                }
            else:
                return {
                    'health': 'healthy',
                    'message': f'Last sync was {hours_since_sync:.1f} hours ago',
                    'last_sync': status.get('last_sync_time'),
                    'sync_results': status.get('results', {})
                }
        
        return {
            'health': 'unknown',
            'message': 'Unable to determine sync health',
            'status': status
        }
        
    except Exception as e:
        logger.error(f"Error checking cron health: {str(e)}")
        return {
            'health': 'error',
            'message': 'Failed to check cron health',
            'error': str(e)
        }

def get_sync_logs(lines: int = 50) -> Dict[str, Any]:
    """
    Get recent sync log entries
    
    Args:
        lines: Number of recent log lines to return
        
    Returns:
        Dict containing log entries
    """
    try:
        project_root = Path(__file__).parent
        log_file = project_root / 'logs' / 'sync' / 'sync_operations.log'
        
        if not log_file.exists():
            return {
                'logs': [],
                'message': 'No sync logs found'
            }
        
        # Read last N lines from log file
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        # Parse JSON log entries
        log_entries = []
        for line in recent_lines:
            try:
                if line.strip():
                    log_entry = json.loads(line.strip())
                    log_entries.append(log_entry)
            except json.JSONDecodeError:
                # Skip non-JSON lines
                continue
        
        return {
            'logs': log_entries,
            'total_lines': len(recent_lines),
            'log_file': str(log_file)
        }
        
    except Exception as e:
        logger.error(f"Error reading sync logs: {str(e)}")
        return {
            'logs': [],
            'error': str(e),
            'message': 'Failed to read sync logs'
        }
