"""
Authentication Utilities Module

Provides password hashing, JWT token management, and authentication utilities
for the Pinecone E-commerce Assistant application.
"""

import os
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Tuple, List, Dict
from jose import JWTError, jwt
from passlib.context import CryptContext
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-super-secret-jwt-key-change-this-in-production")
ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a plain password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)

def create_access_token(data: Dict[str, str], expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[Dict[str, str]]:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

def extract_user_from_token(token: str) -> Optional[Dict[str, str]]:
    """Extract user information from JWT token"""
    payload = verify_token(token)
    if payload is None:
        return None

    username: Optional[str] = payload.get("sub")
    user_id: Optional[int] = payload.get("user_id")

    if username is None or user_id is None:
        return None

    return {"username": username, "user_id": str(user_id)}

def is_token_expired(token: str) -> bool:
    """Check if token is expired"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        exp = payload.get("exp")
        if exp is None:
            return True

        # Check if token is expired
        return datetime.now(timezone.utc) > datetime.fromtimestamp(exp, tz=timezone.utc)
    except JWTError:
        return True

def get_token_expiry_time() -> int:
    """Get token expiry time in seconds"""
    return ACCESS_TOKEN_EXPIRE_MINUTES * 60

def validate_password_strength(password: str) -> Tuple[bool, str]:
    """Validate password strength and return result with message"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"

    if not any(c.isupper() for c in password):
        return False, "Password must contain at least one uppercase letter"

    if not any(c.islower() for c in password):
        return False, "Password must contain at least one lowercase letter"

    if not any(c.isdigit() for c in password):
        return False, "Password must contain at least one digit"

    # Check for special characters (optional but recommended)
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(c in special_chars for c in password):
        return True, "Password is strong (consider adding special characters for extra security)"

    return True, "Password is strong"

def generate_username_suggestions(base_username: str, existing_usernames: List[str]) -> List[str]:
    """Generate username suggestions if the desired username is taken"""
    suggestions = []

    # Try with numbers
    for i in range(1, 10):
        suggestion = f"{base_username}{i}"
        if suggestion not in existing_usernames:
            suggestions.append(suggestion)

    # Try with underscores and numbers
    for i in range(1, 5):
        suggestion = f"{base_username}_{i}"
        if suggestion not in existing_usernames:
            suggestions.append(suggestion)

    return suggestions[:5]  # Return top 5 suggestions
