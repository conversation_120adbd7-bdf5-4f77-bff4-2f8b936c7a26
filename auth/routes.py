from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from datetime import timed<PERSON><PERSON>

from database.session import get_db
from database.crud import UserCRUD
from database.models import User
from .models import User<PERSON><PERSON>, User<PERSON>ogin, UserResponse, Token, PasswordChange, UserUpdate
from .utils import (
    verify_password,
    get_password_hash,
    create_access_token,
    get_token_expiry_time,
    validate_password_strength,
    generate_username_suggestions
)
from .dependencies import get_current_active_user

router = APIRouter(prefix="/auth", tags=["authentication"])
security = HTTPBearer()

@router.post("/register", response_model=Token, status_code=status.HTTP_201_CREATED)
async def register_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """
    Register a new user account
    """
    # Check if username already exists
    existing_user = UserCRUD.get_user_by_username(db, username=user_data.username)
    if existing_user:
        # Generate username suggestions
        existing_usernames = [u.username for u in db.query(User).all()]
        suggestions = generate_username_suggestions(user_data.username, existing_usernames)

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Username already exists",
                "suggestions": suggestions
            }
        )

    # Check if email already exists
    existing_email = UserCRUD.get_user_by_email(db, email=user_data.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Validate password strength
    is_strong, message = validate_password_strength(user_data.password)
    if not is_strong:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Password validation failed: {message}"
        )

    # Hash password and create user
    hashed_password = get_password_hash(user_data.password)

    try:
        user = UserCRUD.create_user(
            db=db,
            username=user_data.username,
            email=user_data.email,
            password_hash=hashed_password
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user account"
        )

    # Create access token
    access_token_expires = timedelta(minutes=get_token_expiry_time() // 60)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id},
        expires_delta=access_token_expires
    )

    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=get_token_expiry_time(),
        user=UserResponse.from_orm(user)
    )

@router.post("/login", response_model=Token)
async def login_user(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """
    Authenticate user and return access token
    """
    # Get user by username
    user = UserCRUD.get_user_by_username(db, username=user_credentials.username)

    # Check if user exists and password is correct
    if not user or not verify_password(user_credentials.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is inactive"
        )

    # Create access token
    access_token_expires = timedelta(minutes=get_token_expiry_time() // 60)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id},
        expires_delta=access_token_expires
    )

    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=get_token_expiry_time(),
        user=UserResponse.from_orm(user)
    )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """
    Get current user information
    """
    return UserResponse.from_orm(current_user)

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user information
    """
    update_data = user_update.dict(exclude_unset=True)

    # Check if email is being updated and if it's already taken
    if "email" in update_data:
        existing_email = UserCRUD.get_user_by_email(db, email=update_data["email"])
        if existing_email and existing_email.id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

    # Update user
    updated_user = UserCRUD.update_user(db, user_id=current_user.id, **update_data)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )

    return UserResponse.from_orm(updated_user)

@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Change user password
    """
    # Verify current password
    if not verify_password(password_data.current_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )

    # Validate new password strength
    is_strong, message = validate_password_strength(password_data.new_password)
    if not is_strong:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"New password validation failed: {message}"
        )

    # Hash new password and update
    new_password_hash = get_password_hash(password_data.new_password)
    updated_user = UserCRUD.update_user(
        db,
        user_id=current_user.id,
        password_hash=new_password_hash
    )

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update password"
        )

    return {"message": "Password updated successfully"}

@router.post("/logout")
async def logout_user(current_user: User = Depends(get_current_active_user)):
    """
    Logout user (client should discard the token)
    """
    return {"message": "Successfully logged out"}

@router.put("/profile", response_model=UserResponse)
async def update_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update user profile (alias for /me endpoint)
    """
    return await update_current_user(user_update, current_user, db)

@router.get("/validate-token")
async def validate_token(current_user: User = Depends(get_current_active_user)):
    """
    Validate if the current token is still valid
    """
    return {
        "valid": True,
        "user": UserResponse.from_orm(current_user)
    }
