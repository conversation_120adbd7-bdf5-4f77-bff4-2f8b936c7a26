from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional

from database.session import get_db
from database.crud import UserCRUD
from database.models import User
from .utils import extract_user_from_token, is_token_expired

# Security scheme for JWT tokens
security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    Dependency to get the current authenticated user from JWT token
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Extract token from credentials
        token = credentials.credentials
        
        # Check if token is expired
        if is_token_expired(token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Extract user information from token
        user_data = extract_user_from_token(token)
        if user_data is None:
            raise credentials_exception
        
        username = user_data.get("username")
        user_id = user_data.get("user_id")
        
        if username is None or user_id is None:
            raise credentials_exception
            
    except Exception:
        raise credentials_exception
    
    # Get user from database
    user = UserCRUD.get_user_by_id(db, user_id=user_id)
    if user is None:
        raise credentials_exception
    
    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is inactive"
        )
    
    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Dependency to get the current active user
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Dependency to optionally get the current user (for endpoints that work with or without auth)
    """
    if credentials is None:
        return None
    
    try:
        token = credentials.credentials
        
        # Check if token is expired
        if is_token_expired(token):
            return None
        
        # Extract user information from token
        user_data = extract_user_from_token(token)
        if user_data is None:
            return None
        
        username = user_data.get("username")
        user_id = user_data.get("user_id")
        
        if username is None or user_id is None:
            return None
        
        # Get user from database
        user = UserCRUD.get_user_by_id(db, user_id=user_id)
        if user is None or not user.is_active:
            return None
        
        return user
        
    except Exception:
        return None

class RequirePermissions:
    """
    Dependency class for checking user permissions
    """
    def __init__(self, *required_permissions):
        self.required_permissions = required_permissions
    
    def __call__(self, current_user: User = Depends(get_current_active_user)):
        # For now, we'll implement basic permission checking
        # In the future, this can be extended with role-based permissions
        
        # Check if user is active (basic permission)
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User does not have required permissions"
            )
        
        return current_user

# Common permission dependencies
require_authenticated = Depends(get_current_active_user)
require_optional_auth = Depends(get_optional_current_user)
