{"timestamp": "2025-05-27T08:53:54.906362+00:00", "level": "ERROR", "logger": "error", "message": "Test error occurred", "module": "test_logging", "function": "test_production_logging", "line": 79, "thread": 8549255232, "thread_name": "MainThread", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Downloads/Python_Training/AI Tools/Pinecone Assistant Ecom 2/test_logging.py\", line 77, in test_production_logging\n    raise ValueError(\"This is a test error\")\nValueError: This is a test error", "extra": {"error_type": "ValueError", "error_message": "This is a test error"}, "user_id": 456, "store_id": 123}