{"timestamp": "2025-05-27T08:53:54.906533+00:00", "level": "INFO", "logger": "scheduler", "message": "Scheduler job started", "module": "test_logging", "function": "test_production_logging", "line": 86, "thread": 8549255232, "thread_name": "MainThread", "extra": {"job_id": "auto_sync_stores", "trigger_time": "12:00:00 UTC", "stores_count": 5}}
{"timestamp": "2025-05-27T09:41:25.225829+00:00", "level": "INFO", "logger": "scheduler", "message": "Scheduled auto-sync job: Every day at 12:00 AM and 12:00 PM UTC", "module": "scheduler", "function": "_setup_jobs", "line": 73, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:41:25.226162+00:00", "level": "INFO", "logger": "scheduler", "message": "AutoSyncScheduler initialized", "module": "scheduler", "function": "__init__", "line": 59, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:41:25.226420+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\ude80 AutoSyncScheduler started successfully", "module": "scheduler", "function": "start", "line": 198, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:41:25.226451+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\udcc5 Next sync scheduled for: 12:00 AM and 12:00 PM UTC daily", "module": "scheduler", "function": "start", "line": 199, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:41:25.226481+00:00", "level": "INFO", "logger": "scheduler", "message": "\u23f0 Next auto-sync: 2025-05-28 00:00:00+05:30", "module": "scheduler", "function": "start", "line": 205, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:44:33.212550+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\uded1 AutoSyncScheduler stopped", "module": "scheduler", "function": "stop", "line": 215, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:36.224817+00:00", "level": "INFO", "logger": "scheduler", "message": "Scheduled auto-sync job: Every day at 12:00 AM and 12:00 PM UTC", "module": "scheduler", "function": "_setup_jobs", "line": 73, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:36.225038+00:00", "level": "INFO", "logger": "scheduler", "message": "AutoSyncScheduler initialized", "module": "scheduler", "function": "__init__", "line": 59, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:36.225251+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\ude80 AutoSyncScheduler started successfully", "module": "scheduler", "function": "start", "line": 198, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:36.225296+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\udcc5 Next sync scheduled for: 12:00 AM and 12:00 PM UTC daily", "module": "scheduler", "function": "start", "line": 199, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:36.225329+00:00", "level": "INFO", "logger": "scheduler", "message": "\u23f0 Next auto-sync: 2025-05-28 00:00:00+05:30", "module": "scheduler", "function": "start", "line": 205, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:38.349691+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\uded1 AutoSyncScheduler stopped", "module": "scheduler", "function": "stop", "line": 215, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:41.145977+00:00", "level": "INFO", "logger": "scheduler", "message": "Scheduled auto-sync job: Every day at 12:00 AM and 12:00 PM UTC", "module": "scheduler", "function": "_setup_jobs", "line": 73, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:41.146095+00:00", "level": "INFO", "logger": "scheduler", "message": "AutoSyncScheduler initialized", "module": "scheduler", "function": "__init__", "line": 59, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:41.146382+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\ude80 AutoSyncScheduler started successfully", "module": "scheduler", "function": "start", "line": 198, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:41.146445+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\udcc5 Next sync scheduled for: 12:00 AM and 12:00 PM UTC daily", "module": "scheduler", "function": "start", "line": 199, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T09:47:41.146478+00:00", "level": "INFO", "logger": "scheduler", "message": "\u23f0 Next auto-sync: 2025-05-28 00:00:00+05:30", "module": "scheduler", "function": "start", "line": 205, "thread": 8549255232, "thread_name": "MainThread"}
{"timestamp": "2025-05-27T10:02:58.714023+00:00", "level": "INFO", "logger": "scheduler", "message": "\ud83d\uded1 AutoSyncScheduler stopped", "module": "scheduler", "function": "stop", "line": 215, "thread": 8549255232, "thread_name": "MainThread"}
