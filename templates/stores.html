<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Management - Pinecone E-commerce Assistant</title>
    <link href="/static/css/styles.css" rel="stylesheet">
    <style>
        .stores-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .stores-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn:disabled {
            background-color: #6c757d !important;
            color: #fff !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }
        .btn:disabled:hover {
            background-color: #6c757d !important;
            transform: none !important;
        }
        .stores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .store-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        .store-card:hover {
            transform: translateY(-2px);
        }
        .store-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .store-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0 0 5px 0;
            color: #333;
        }
        .store-url {
            color: #666;
            font-size: 0.9rem;
        }
        .store-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-top: 10px;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-syncing {
            background-color: #fff3cd;
            color: #856404;
        }
        .store-stats {
            padding: 15px 20px;
            background-color: #f8f9fa;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            text-align: center;
        }
        .stat-item {
            padding: 10px;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 0.8rem;
            color: #666;
            text-transform: uppercase;
        }
        .store-actions {
            padding: 15px 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .empty-state i {
            font-size: 64px;
            color: #ccc;
            margin-bottom: 20px;
        }
        .add-store-form {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: none;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        .form-actions {
            display: flex;
            gap: 10px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #f5c6cb;
            display: none;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #c3e6cb;
            display: none;
        }
        .connection-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab-btn {
            background: none;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 14px;
            color: #666;
            transition: all 0.3s ease;
        }
        .tab-btn.active {
            color: #007bff;
            border-bottom-color: #007bff;
            font-weight: bold;
        }
        .tab-btn:hover {
            color: #007bff;
            background-color: #f8f9fa;
        }
        .connection-form {
            transition: opacity 0.3s ease;
        }
        @media (max-width: 768px) {
            .stores-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            .stores-grid {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: 1fr;
            }
            .store-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="stores-container">
        <!-- Header -->
        <div class="stores-header">
            <div>
                <h1>Store Management</h1>
                <p>Connect and manage your Shopify stores</p>
            </div>
            <div>
                <button id="add-store-btn" class="btn btn-primary">
                    Add Store
                </button>
                <a href="/dashboard" class="btn btn-secondary">
                    Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Messages -->
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <!-- Add Store Form -->
        <div id="add-store-form" class="add-store-form">
            <h3>Connect New Store</h3>

            <!-- Manual Connection Form -->
            <div id="manual-form" class="connection-form">
                <p style="color: #666; margin-bottom: 20px;">
                    Connect using a private app access token. You'll need to create a private app in your Shopify admin first.
                </p>
                <form id="manual-store-form">
                    <div class="form-group">
                        <label for="shop-url">Shop URL:</label>
                        <input type="text" id="shop-url" name="shop_url" placeholder="mystore.myshopify.com" required>
                        <small style="color: #666; font-size: 12px;">Enter your Shopify store URL (e.g., mystore.myshopify.com)</small>
                    </div>

                    <div class="form-group">
                        <label for="access-token">Access Token:</label>
                        <input type="password" id="access-token" name="access_token" placeholder="shpat_..." required>
                        <small style="color: #666; font-size: 12px;">Your Shopify private app access token</small>
                    </div>

                    <div class="loading" id="form-loading">
                        <div class="spinner"></div>
                        Testing connection...
                    </div>

                    <div class="form-actions">
                        <button type="button" id="test-connection-btn" class="btn btn-warning">
                            Test Connection
                        </button>
                        <button type="submit" class="btn btn-success" id="save-store-btn" disabled>
                            Save Store
                        </button>
                        <button type="button" id="cancel-btn" class="btn btn-secondary">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Stores Grid -->
        <div id="stores-grid" class="stores-grid">
            <!-- Stores will be loaded here -->
        </div>

        <!-- Empty State -->
        <div id="empty-state" class="empty-state" style="display: none;">
            <div style="font-size: 64px; margin-bottom: 20px;">🏪</div>
            <h3>No Stores Connected</h3>
            <p>Connect your first Shopify store to get started with AI-powered customer assistance.</p>
            <button onclick="showAddStoreForm()" class="btn btn-primary" style="margin-top: 20px;">
                Connect Your First Store
            </button>
        </div>
    </div>

    <script src="/static/js/auth.js"></script>
    <script src="/static/js/stores.js"></script>
</body>
</html>
