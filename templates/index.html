<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinecone E-commerce Assistant</title>
    <link href="/static/css/styles.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }
        .hero-section {
            padding: 60px 0;
        }
        .hero-section h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        .hero-section p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .auth-section {
            margin: 50px 0;
        }
        .auth-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
            transform: translateY(-2px);
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
            transform: translateY(-2px);
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }
        .feature {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .feature h3 {
            color: #ffd700;
            margin-bottom: 15px;
        }
        @media (max-width: 768px) {
            .hero-section h1 {
                font-size: 2.5rem;
            }
            .auth-buttons {
                flex-direction: column;
                align-items: center;
            }
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero-section">
            <h1>Pinecone E-commerce Assistant</h1>
            <p>Multi-store intelligent shopping assistant powered by Pinecone and AI</p>
        </div>

        <!-- Authentication Section -->
        <div class="auth-section">
            <div id="logged-out-section">
                <h2>Get Started</h2>
                <p>Create an account or login to manage your stores and AI assistants</p>
                <div class="auth-buttons">
                    <a href="/register" class="btn btn-primary">
                        Create Account
                    </a>
                    <a href="/login" class="btn btn-secondary">
                        Login
                    </a>
                </div>
            </div>

            <div id="logged-in-section" style="display: none;">
                <h2>Welcome back!</h2>
                <p>Ready to manage your stores and chat with your AI assistants?</p>
                <div class="auth-buttons">
                    <a href="/dashboard" class="btn btn-success">
                        Go to Dashboard
                    </a>
                    <button onclick="logout()" class="btn btn-danger">
                        Logout
                    </button>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="features">
            <div class="feature">
                <h3>🏪 Multi-Store Management</h3>
                <p>Connect and manage multiple Shopify stores from a single dashboard. Each store gets its own AI assistant.</p>
            </div>
            <div class="feature">
                <h3>🤖 AI-Powered Assistants</h3>
                <p>Intelligent assistants trained on your store data to help customers find products and answer questions.</p>
            </div>
            <div class="feature">
                <h3>🔒 Secure & Private</h3>
                <p>Your store data is encrypted and secure. Each user has access only to their own stores and data.</p>
            </div>
        </div>
    </div>

    <script>
        // Check authentication status and show appropriate section
        function checkAuthStatus() {
            const token = localStorage.getItem('access_token');
            const loggedOutSection = document.getElementById('logged-out-section');
            const loggedInSection = document.getElementById('logged-in-section');

            if (token) {
                loggedOutSection.style.display = 'none';
                loggedInSection.style.display = 'block';
            } else {
                loggedOutSection.style.display = 'block';
                loggedInSection.style.display = 'none';
            }
        }

        // Logout function
        function logout() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('user_data');
            checkAuthStatus();
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', checkAuthStatus);
    </script>
</body>
</html>
