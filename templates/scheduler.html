<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Sync Scheduler - Pinecone E-commerce Assistant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }
        .status-running {
            background-color: #28a745;
            color: white;
        }
        .status-stopped {
            background-color: #dc3545;
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-warning {
            background-color: #ffc107;
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            color: #212529;
        }
        .info-item {
            padding: 1rem;
            border-left: 4px solid #667eea;
            background-color: #f8f9fa;
            margin-bottom: 1rem;
            border-radius: 0 10px 10px 0;
        }
        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }
        .refresh-btn:hover {
            transform: scale(1.1);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header text-center">
                        <h1><i class="fas fa-clock me-3"></i>Auto-Sync Scheduler</h1>
                        <p class="mb-0">Automatic store synchronization every 12 hours at 12:00 AM and 12:00 PM UTC</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <a href="/dashboard" class="btn btn-outline-light me-3">
                    <i class="fas fa-home me-2"></i>Dashboard
                </a>
                <a href="/stores" class="btn btn-outline-light me-3">
                    <i class="fas fa-store me-2"></i>Stores
                </a>
                <a href="/assistants" class="btn btn-outline-light">
                    <i class="fas fa-robot me-2"></i>Assistants
                </a>
            </div>
        </div>

        <!-- Scheduler Status -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle me-2"></i>Scheduler Status</h3>
                    </div>
                    <div class="card-body">
                        <div id="scheduler-status">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading scheduler status...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-cogs me-2"></i>Actions</h3>
                    </div>
                    <div class="card-body">
                        <button id="trigger-sync-btn" class="btn btn-warning w-100 mb-3" onclick="triggerManualSync()">
                            <i class="fas fa-sync me-2"></i>Trigger Manual Sync
                        </button>
                        <button class="btn btn-primary w-100" onclick="refreshStatus()">
                            <i class="fas fa-refresh me-2"></i>Refresh Status
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Job Details -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-list me-2"></i>Scheduled Jobs</h3>
                    </div>
                    <div class="card-body">
                        <div id="job-details">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading job details...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Refresh Button -->
    <button class="refresh-btn" onclick="refreshStatus()" title="Refresh Status">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Load scheduler status on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            // Auto-refresh every 30 seconds
            setInterval(refreshStatus, 30000);
        });

        async function refreshStatus() {
            try {
                const response = await fetch('/api/scheduler/status');
                const status = await response.json();
                
                displaySchedulerStatus(status);
                displayJobDetails(status);
                
            } catch (error) {
                console.error('Error fetching scheduler status:', error);
                document.getElementById('scheduler-status').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading scheduler status: ${error.message}
                    </div>
                `;
            }
        }

        function displaySchedulerStatus(status) {
            const statusHtml = `
                <div class="info-item">
                    <h5><i class="fas fa-power-off me-2"></i>Status</h5>
                    <span class="status-badge ${status.running ? 'status-running' : 'status-stopped'}">
                        ${status.running ? 'RUNNING' : 'STOPPED'}
                    </span>
                </div>
                <div class="info-item">
                    <h5><i class="fas fa-tasks me-2"></i>Active Jobs</h5>
                    <strong>${status.jobs_count || 0}</strong>
                </div>
                <div class="info-item">
                    <h5><i class="fas fa-clock me-2"></i>Next Sync</h5>
                    <strong>${status.next_run_time ? new Date(status.next_run_time).toLocaleString() : 'Not scheduled'}</strong>
                </div>
            `;
            
            document.getElementById('scheduler-status').innerHTML = statusHtml;
        }

        function displayJobDetails(status) {
            if (!status.jobs || status.jobs.length === 0) {
                document.getElementById('job-details').innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No scheduled jobs found.
                    </div>
                `;
                return;
            }

            let jobsHtml = '';
            status.jobs.forEach(job => {
                jobsHtml += `
                    <div class="info-item">
                        <h5><i class="fas fa-calendar me-2"></i>${job.name}</h5>
                        <p><strong>ID:</strong> ${job.id}</p>
                        <p><strong>Trigger:</strong> ${job.trigger}</p>
                        <p><strong>Next Run:</strong> ${job.next_run_time ? new Date(job.next_run_time).toLocaleString() : 'Not scheduled'}</p>
                    </div>
                `;
            });

            document.getElementById('job-details').innerHTML = jobsHtml;
        }

        async function triggerManualSync() {
            const btn = document.getElementById('trigger-sync-btn');
            const originalText = btn.innerHTML;
            
            try {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Triggering Sync...';
                
                const response = await fetch('/api/scheduler/trigger', {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    btn.innerHTML = '<i class="fas fa-check me-2"></i>Sync Triggered!';
                    btn.className = 'btn btn-success w-100 mb-3';
                    
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.className = 'btn btn-warning w-100 mb-3';
                        btn.disabled = false;
                    }, 3000);
                } else {
                    throw new Error(result.detail || 'Failed to trigger sync');
                }
                
            } catch (error) {
                console.error('Error triggering manual sync:', error);
                btn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Error!';
                btn.className = 'btn btn-danger w-100 mb-3';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.className = 'btn btn-warning w-100 mb-3';
                    btn.disabled = false;
                }, 3000);
            }
        }
    </script>
</body>
</html>
