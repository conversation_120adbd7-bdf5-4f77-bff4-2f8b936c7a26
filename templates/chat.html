<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat - Pinecone E-commerce Assistant</title>
    <link href="/static/css/styles.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .chat-header {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chat-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .store-selector {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            font-size: 14px;
        }
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
        }
        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        .message.user {
            flex-direction: row-reverse;
        }
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            flex-shrink: 0;
        }
        .message.user .message-avatar {
            background-color: #007bff;
        }
        .message.assistant .message-avatar {
            background-color: #28a745;
        }
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        .message.user .message-content {
            background-color: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }
        .message.assistant .message-content {
            background-color: #f1f1f1;
            color: #333;
            border-bottom-left-radius: 4px;
        }
        .message-time {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        .chat-input-container {
            padding: 20px;
            border-top: 1px solid #ddd;
            background: white;
        }
        .chat-input-form {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            resize: none;
            max-height: 100px;
            font-family: inherit;
            font-size: 14px;
        }
        .chat-input:focus {
            outline: none;
            border-color: #007bff;
        }
        .send-button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }
        .send-button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        .send-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background-color: #f1f1f1;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 70%;
            margin-bottom: 20px;
        }
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #999;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 20px;
            border: 1px solid #f5c6cb;
            display: none;
        }
        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <div class="chat-header">
        <div class="chat-title">
            <h2 style="margin: 0;">💬 Chat with Assistant</h2>
            <select id="store-selector" class="store-selector">
                <option value="">Select a store...</option>
            </select>
        </div>
        <div>
            <a href="/dashboard" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <div class="chat-container">
        <div id="error-message" class="error-message"></div>
        
        <div class="chat-messages" id="chat-messages">
            <div class="welcome-message" id="welcome-message">
                <h3>Welcome to your AI Shopping Assistant!</h3>
                <p>Select a store above to start chatting with your store-specific assistant.</p>
                <p>Your assistant can help customers find products, answer questions, and provide personalized recommendations.</p>
            </div>
        </div>

        <div class="chat-input-container">
            <form class="chat-input-form" id="chat-form">
                <textarea 
                    id="chat-input" 
                    class="chat-input" 
                    placeholder="Type your message..." 
                    rows="1"
                    disabled
                ></textarea>
                <button type="submit" class="send-button" id="send-button" disabled>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </form>
        </div>
    </div>

    <script src="/static/js/auth.js"></script>
    <script>
        let currentStoreId = null;
        let conversationId = null;
        let stores = [];

        // Initialize chat page
        function initChatPage() {
            // Require authentication
            if (!requireAuth()) return;
            
            // Load stores for selection
            loadStores();
            
            // Setup event listeners
            setupEventListeners();
        }

        // Setup event listeners
        function setupEventListeners() {
            // Store selector change
            document.getElementById('store-selector').addEventListener('change', function(e) {
                selectStore(e.target.value);
            });

            // Chat form submission
            document.getElementById('chat-form').addEventListener('submit', function(e) {
                e.preventDefault();
                sendMessage();
            });

            // Auto-resize textarea
            document.getElementById('chat-input').addEventListener('input', function(e) {
                e.target.style.height = 'auto';
                e.target.style.height = Math.min(e.target.scrollHeight, 100) + 'px';
            });

            // Enter key to send (Shift+Enter for new line)
            document.getElementById('chat-input').addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // Load stores for selection
        async function loadStores() {
            try {
                const response = await fetch('/assistants/stores/selection', {
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    stores = await response.json();
                    populateStoreSelector();
                } else {
                    showError('Failed to load stores');
                }
            } catch (error) {
                showError('Network error while loading stores');
            }
        }

        // Populate store selector dropdown
        function populateStoreSelector() {
            const selector = document.getElementById('store-selector');
            selector.innerHTML = '<option value="">Select a store...</option>';

            stores.forEach(store => {
                const option = document.createElement('option');
                option.value = store.store_id;
                option.textContent = `${store.store_name} (${store.has_assistant ? 'Assistant Ready' : 'No Assistant'})`;
                option.disabled = !store.has_assistant;
                selector.appendChild(option);
            });
        }

        // Select a store
        function selectStore(storeId) {
            if (!storeId) {
                currentStoreId = null;
                conversationId = null;
                document.getElementById('chat-input').disabled = true;
                document.getElementById('send-button').disabled = true;
                clearMessages();
                showWelcomeMessage();
                return;
            }

            currentStoreId = parseInt(storeId);
            conversationId = null; // Reset conversation
            
            const store = stores.find(s => s.store_id === currentStoreId);
            if (store && store.has_assistant) {
                document.getElementById('chat-input').disabled = false;
                document.getElementById('send-button').disabled = false;
                clearMessages();
                addAssistantMessage(`Hello! I'm the AI assistant for ${store.store_name}. How can I help you today?`);
                document.getElementById('chat-input').focus();
            } else {
                showError('This store does not have an assistant yet. Please connect the store first.');
            }
        }

        // Send message
        async function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message || !currentStoreId) return;

            // Add user message to chat
            addUserMessage(message);
            input.value = '';
            input.style.height = 'auto';

            // Show typing indicator
            showTypingIndicator();

            try {
                const response = await fetch('/assistants/chat', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        message: message,
                        store_id: currentStoreId,
                        conversation_id: conversationId
                    })
                });

                hideTypingIndicator();

                if (response.ok) {
                    const data = await response.json();
                    conversationId = data.conversation_id;
                    addAssistantMessage(data.response);
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to send message');
                }
            } catch (error) {
                hideTypingIndicator();
                showError('Network error while sending message');
            }
        }

        // Add user message to chat
        function addUserMessage(message) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user';
            messageDiv.innerHTML = `
                <div class="message-avatar">U</div>
                <div class="message-content">
                    ${escapeHtml(message)}
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            `;
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // Add assistant message to chat
        function addAssistantMessage(message) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant';
            messageDiv.innerHTML = `
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    ${escapeHtml(message)}
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            `;
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // Show typing indicator
        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chat-messages');
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typing-indicator';
            typingDiv.className = 'message assistant';
            typingDiv.innerHTML = `
                <div class="message-avatar">🤖</div>
                <div class="typing-indicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(typingDiv);
            scrollToBottom();
        }

        // Hide typing indicator
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // Clear messages
        function clearMessages() {
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.innerHTML = '';
            hideWelcomeMessage();
        }

        // Show welcome message
        function showWelcomeMessage() {
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.innerHTML = `
                <div class="welcome-message" id="welcome-message">
                    <h3>Welcome to your AI Shopping Assistant!</h3>
                    <p>Select a store above to start chatting with your store-specific assistant.</p>
                    <p>Your assistant can help customers find products, answer questions, and provide personalized recommendations.</p>
                </div>
            `;
        }

        // Hide welcome message
        function hideWelcomeMessage() {
            const welcomeMessage = document.getElementById('welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }
        }

        // Scroll to bottom of messages
        function scrollToBottom() {
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // Escape HTML to prevent XSS
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', initChatPage);
    </script>
</body>
</html>
