"""
Pine<PERSON>e Assistant Limits Manager

This module manages Pinecone Assistant plan limits to ensure compliance with:
- Standard Plan: 150,000 tokens/minute, 10MB file size, 10,000 files, 10GB storage
- File size optimization and chunking
- Token rate limiting
- File count monitoring
"""

import time
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class PineconeLimitsManager:
    """Manages Pinecone Assistant plan limits and optimizations."""
    
    # Standard Plan Limits
    MAX_TOKENS_PER_MINUTE = 150000
    MAX_FILE_SIZE_MB = 10.0
    MAX_FILES_TOTAL = 10000
    MAX_STORAGE_GB = 10.0
    MAX_INPUT_TOKENS_PER_QUERY = 64000
    
    def __init__(self, plan_type: str = "standard"):
        """
        Initialize the limits manager.
        
        Args:
            plan_type: The Pinecone plan type ("starter", "standard", "enterprise")
        """
        self.plan_type = plan_type.lower()
        self.token_usage_history: List[Dict] = []
        self.file_count = 0
        self.storage_used_mb = 0.0
        
        # Set limits based on plan type
        if self.plan_type == "starter":
            self.MAX_TOKENS_PER_MINUTE = 30000
            self.MAX_FILES_TOTAL = 10
            self.MAX_STORAGE_GB = 1.0
        elif self.plan_type == "enterprise":
            self.MAX_TOKENS_PER_MINUTE = 150000  # Same as standard
            self.MAX_FILES_TOTAL = 10000  # Same as standard
            self.MAX_STORAGE_GB = 10.0  # Same as standard
        
        logger.info(f"Initialized Pinecone Limits Manager for {plan_type} plan", extra={
            'extra_data': {
                'max_tokens_per_minute': self.MAX_TOKENS_PER_MINUTE,
                'max_file_size_mb': self.MAX_FILE_SIZE_MB,
                'max_files': self.MAX_FILES_TOTAL,
                'max_storage_gb': self.MAX_STORAGE_GB
            }
        })

    def check_file_size_limit(self, content: str, filename: str) -> Dict:
        """
        Check if file size is within limits.
        
        Args:
            content: File content as string
            filename: Name of the file
            
        Returns:
            Dict with check results and recommendations
        """
        file_size_bytes = len(content.encode('utf-8'))
        file_size_mb = file_size_bytes / (1024 * 1024)
        
        result = {
            'within_limit': file_size_mb <= self.MAX_FILE_SIZE_MB,
            'file_size_mb': round(file_size_mb, 2),
            'limit_mb': self.MAX_FILE_SIZE_MB,
            'needs_chunking': file_size_mb > (self.MAX_FILE_SIZE_MB * 0.95),  # 95% threshold
            'recommended_chunks': 1
        }
        
        if result['needs_chunking']:
            result['recommended_chunks'] = max(1, int(file_size_mb / (self.MAX_FILE_SIZE_MB * 0.95)) + 1)
        
        logger.info(f"File size check for {filename}", extra={
            'extra_data': result
        })
        
        return result

    def check_file_count_limit(self, additional_files: int = 1) -> Dict:
        """
        Check if adding files would exceed the file count limit.
        
        Args:
            additional_files: Number of files to be added
            
        Returns:
            Dict with check results
        """
        new_total = self.file_count + additional_files
        
        result = {
            'within_limit': new_total <= self.MAX_FILES_TOTAL,
            'current_count': self.file_count,
            'additional_files': additional_files,
            'new_total': new_total,
            'limit': self.MAX_FILES_TOTAL,
            'remaining_capacity': max(0, self.MAX_FILES_TOTAL - self.file_count)
        }
        
        logger.info("File count check", extra={'extra_data': result})
        return result

    def check_storage_limit(self, additional_size_mb: float = 0) -> Dict:
        """
        Check if adding storage would exceed the storage limit.
        
        Args:
            additional_size_mb: Additional storage in MB
            
        Returns:
            Dict with check results
        """
        storage_limit_mb = self.MAX_STORAGE_GB * 1024
        new_total_mb = self.storage_used_mb + additional_size_mb
        
        result = {
            'within_limit': new_total_mb <= storage_limit_mb,
            'current_storage_mb': round(self.storage_used_mb, 2),
            'additional_storage_mb': round(additional_size_mb, 2),
            'new_total_mb': round(new_total_mb, 2),
            'limit_mb': storage_limit_mb,
            'remaining_capacity_mb': max(0, storage_limit_mb - self.storage_used_mb)
        }
        
        logger.info("Storage check", extra={'extra_data': result})
        return result

    def check_token_rate_limit(self, estimated_tokens: int) -> Dict:
        """
        Check if the token usage is within rate limits.
        
        Args:
            estimated_tokens: Estimated tokens for the operation
            
        Returns:
            Dict with rate limit status and wait time if needed
        """
        current_time = datetime.now()
        one_minute_ago = current_time - timedelta(minutes=1)
        
        # Remove old entries
        self.token_usage_history = [
            entry for entry in self.token_usage_history 
            if entry['timestamp'] > one_minute_ago
        ]
        
        # Calculate current usage in the last minute
        current_usage = sum(entry['tokens'] for entry in self.token_usage_history)
        
        result = {
            'within_limit': (current_usage + estimated_tokens) <= self.MAX_TOKENS_PER_MINUTE,
            'current_usage': current_usage,
            'estimated_tokens': estimated_tokens,
            'new_total': current_usage + estimated_tokens,
            'limit': self.MAX_TOKENS_PER_MINUTE,
            'wait_seconds': 0
        }
        
        if not result['within_limit']:
            # Calculate wait time until oldest entry expires
            if self.token_usage_history:
                oldest_entry = min(self.token_usage_history, key=lambda x: x['timestamp'])
                wait_until = oldest_entry['timestamp'] + timedelta(minutes=1)
                wait_seconds = max(0, (wait_until - current_time).total_seconds())
                result['wait_seconds'] = int(wait_seconds) + 1
        
        logger.info("Token rate limit check", extra={'extra_data': result})
        return result

    def record_token_usage(self, tokens_used: int):
        """Record token usage for rate limiting."""
        self.token_usage_history.append({
            'timestamp': datetime.now(),
            'tokens': tokens_used
        })

    def update_file_count(self, count_change: int):
        """Update the file count."""
        self.file_count = max(0, self.file_count + count_change)
        logger.info(f"Updated file count: {self.file_count}")

    def update_storage_usage(self, size_change_mb: float):
        """Update the storage usage."""
        self.storage_used_mb = max(0, self.storage_used_mb + size_change_mb)
        logger.info(f"Updated storage usage: {self.storage_used_mb:.2f} MB")

    def get_usage_summary(self) -> Dict:
        """Get a summary of current usage against limits."""
        current_time = datetime.now()
        one_minute_ago = current_time - timedelta(minutes=1)
        
        # Calculate recent token usage
        recent_tokens = sum(
            entry['tokens'] for entry in self.token_usage_history 
            if entry['timestamp'] > one_minute_ago
        )
        
        return {
            'plan_type': self.plan_type,
            'tokens': {
                'recent_usage': recent_tokens,
                'limit_per_minute': self.MAX_TOKENS_PER_MINUTE,
                'utilization_percent': round((recent_tokens / self.MAX_TOKENS_PER_MINUTE) * 100, 1)
            },
            'files': {
                'current_count': self.file_count,
                'limit': self.MAX_FILES_TOTAL,
                'utilization_percent': round((self.file_count / self.MAX_FILES_TOTAL) * 100, 1)
            },
            'storage': {
                'current_mb': round(self.storage_used_mb, 2),
                'limit_mb': self.MAX_STORAGE_GB * 1024,
                'utilization_percent': round((self.storage_used_mb / (self.MAX_STORAGE_GB * 1024)) * 100, 1)
            }
        }

    def estimate_tokens_from_text(self, text: str) -> int:
        """
        Estimate token count from text (rough approximation).
        
        Args:
            text: Text content
            
        Returns:
            Estimated token count
        """
        # Rough estimation: ~4 characters per token for English text
        return len(text) // 4

    def wait_for_rate_limit(self, wait_seconds: int):
        """Wait for rate limit to reset."""
        if wait_seconds > 0:
            logger.info(f"Rate limit reached, waiting {wait_seconds} seconds...")
            time.sleep(wait_seconds)
