# Pinecone E-commerce Assistant

A sophisticated multi-store e-commerce assistant application that integrates Shopify stores with Pinecone AI to provide intelligent customer support and data management.

## 🚀 Features

- **Multi-Store Support**: Connect and manage multiple Shopify stores from a single interface
- **AI-Powered Assistant**: Pinecone-based AI assistant for intelligent customer support
- **GraphQL Integration**: Modern Shopify GraphQL API integration for optimal performance
- **User Management**: Comprehensive authentication and user management system
- **Real-time Data Sync**: Automatic synchronization of products, orders, and customer data
- **Secure Architecture**: JWT-based authentication with encrypted token storage
- **Modern Tech Stack**: Built with FastAPI, SQLAlchemy, and modern Python practices

## 🛠️ Technology Stack

- **Backend**: FastAPI, Python 3.8+
- **Database**: MySQL with SQLAlchemy ORM
- **AI/ML**: Pinecone Vector Database and Assistant API
- **E-commerce**: Shopify GraphQL API
- **Authentication**: JWT with bcrypt password hashing
- **Frontend**: HTML, CSS, JavaScript (Vanilla)
- **Testing**: pytest with comprehensive test coverage

## 📋 Prerequisites

- Python 3.8 or higher
- MySQL 5.7 or higher
- Pinecone account and API key
- Shopify store(s) with private app access tokens

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pinecone-ecommerce-assistant
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration values
   ```

5. **Initialize the database**
   ```bash
   python init_database.py
   ```

6. **Run the application**
   ```bash
   python main.py
   ```

The application will be available at `http://localhost:8000`

## ⚙️ Configuration

### Environment Variables

Key configuration variables in `.env`:

- `PINECONE_API_KEY`: Your Pinecone API key
- `DB_HOST`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`: MySQL database configuration
- `JWT_SECRET_KEY`: Secret key for JWT token signing
- `SHOPIFY_API_VERSION`: Shopify API version (default: 2025-01)

### Database Setup

The application uses MySQL as the primary database. Ensure MySQL is running and accessible with the credentials specified in your `.env` file.

### Shopify Integration

Each store requires:
- Shop URL (e.g., `yourstore.myshopify.com`)
- Private app access token (starting with `shpat_`)

## 🏗️ Architecture

### Multi-Store Architecture

```
User Account
├── Store 1 (Shopify)
│   ├── Products Data
│   ├── Orders Data
│   ├── Customers Data
│   └── Pinecone Assistant
├── Store 2 (Shopify)
│   └── ...
└── Store N (Shopify)
    └── ...
```

### Key Components

- **Authentication System**: JWT-based user authentication
- **Store Manager**: Handles Shopify store connections and data sync
- **Assistant Manager**: Manages Pinecone assistants per store
- **Data Sync Engine**: Synchronizes e-commerce data with Pinecone
- **Chat Interface**: AI-powered customer support interface

## 📚 API Documentation

Once the application is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### Key Endpoints

- `POST /auth/register` - User registration
- `POST /auth/login` - User authentication
- `POST /stores/manual` - Connect Shopify store
- `GET /stores/` - List user's stores
- `POST /assistants/chat` - Chat with AI assistant
- `POST /assistants/{assistant_id}/sync` - Sync store data

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=.

# Run specific test file
pytest tests/test_models.py
```

## 🔒 Security Features

- **Password Security**: bcrypt hashing with salt
- **JWT Tokens**: Secure token-based authentication
- **Input Validation**: Comprehensive Pydantic model validation
- **SQL Injection Protection**: SQLAlchemy ORM with parameterized queries
- **Access Control**: User-based store ownership verification

## 🚀 Deployment

### Production Checklist

1. Set `APP_ENV=production` in environment variables
2. Use strong, unique `JWT_SECRET_KEY`
3. Configure proper CORS origins
4. Set up SSL/TLS certificates
5. Configure database connection pooling
6. Set up monitoring and logging
7. Configure backup strategies

### Docker Deployment (Optional)

```dockerfile
# Dockerfile example
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "main.py"]
```

## 📊 Monitoring

The application includes comprehensive logging:
- Application events
- Database operations
- API requests and responses
- Error tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review existing issues
- Create a new issue with detailed information

## 🔄 Version History

- **v2.0.0**: Multi-store architecture with Pinecone integration
- **v1.0.0**: Initial single-store implementation
