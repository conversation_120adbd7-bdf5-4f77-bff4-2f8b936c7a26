# Core FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.23.2
python-dotenv==1.0.0
python-multipart==0.0.6
jinja2==3.1.2

# HTTP client libraries
httpx==0.25.1
requests>=2.32.3
urllib3==2.0.7

# Pinecone AI dependencies
pinecone>=5.0.0

# Database dependencies
sqlalchemy==2.0.23
alembic==1.12.1
pymysql==1.1.0
cryptography==41.0.7

# Authentication and security dependencies
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
bcrypt==4.3.0

# Data validation and serialization
pydantic[email]>=2.11.4
email-validator>=2.2.0
dnspython>=2.7.0

# Task scheduling - Now using system cron instead of APScheduler
# apscheduler==3.10.1  # Removed - using cron-based scheduling


