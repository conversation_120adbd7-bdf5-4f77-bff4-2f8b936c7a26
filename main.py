"""
Pinecone E-commerce Assistant - Main Application Module

A multi-store e-commerce assistant application with Shopify integration,
Pinecone AI assistant capabilities, and comprehensive user management.
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
from contextlib import asynccontextmanager
import uvicorn
import logging
import time
import uuid
from dotenv import load_dotenv

# Import authentication
from auth.routes import router as auth_router

# Load environment variables
load_dotenv()

# Initialize production logging
from logging_config import init_logging, log_api_request, log_audit_event
init_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Pinecone E-commerce Assistant application")

    # TODO: Add database connection verification
    # TODO: Add Pinecone connection verification
    yield

    # Shutdown
    logger.info("Shutting down Pinecone E-commerce Assistant application")

    # TODO: Add cleanup tasks

# Simple logging middleware function
async def log_requests(request: Request, call_next):
    """Simple request logging middleware"""
    start_time = time.time()
    request_id = str(uuid.uuid4())

    # Process the request
    response = await call_next(request)

    # Log the request
    duration = time.time() - start_time
    method = request.method
    path = str(request.url.path)
    status_code = response.status_code

    # Get user ID from headers if available
    user_id = None
    auth_header = request.headers.get("authorization")
    if auth_header:
        # Could extract user ID from token here if needed
        pass

    try:
        log_api_request(method, path, status_code, duration, user_id, request_id)
    except Exception as e:
        # Don't let logging errors break the request
        logger.error(f"Failed to log request: {e}")

    return response

# Initialize FastAPI app
app = FastAPI(
    title="Pinecone E-commerce Assistant",
    description="Multi-store e-commerce assistant with Shopify integration and AI-powered customer support",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add logging middleware
app.middleware("http")(log_requests)

# Add CORS middleware with more restrictive settings for production
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # TODO: Restrict to specific domains in production
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Include authentication routes
app.include_router(auth_router)

# Include store management routes
from stores.routes import router as stores_router
app.include_router(stores_router)

# Include assistant management routes
from assistants.routes import router as assistants_router
app.include_router(assistants_router)

# Create templates directory for serving HTML
templates = Jinja2Templates(directory="templates")

# Create static directory for serving static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Lifespan events are now handled in the lifespan context manager above

# Define request and response models
class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    conversation_id: str

# Page routes
@app.get("/", response_class=HTMLResponse)
async def get_home_page(request: Request):
    """Home page - redirect to dashboard if authenticated, otherwise show landing page"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/login", response_class=HTMLResponse)
async def get_login_page(request: Request):
    """Login page"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/register", response_class=HTMLResponse)
async def get_register_page(request: Request):
    """Registration page"""
    return templates.TemplateResponse("register.html", {"request": request})

@app.get("/dashboard", response_class=HTMLResponse)
async def get_dashboard_page(request: Request):
    """Dashboard page - requires authentication (handled by frontend)"""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/stores", response_class=HTMLResponse)
async def get_stores_page(request: Request):
    """Store management page - requires authentication (handled by frontend)"""
    return templates.TemplateResponse("stores.html", {"request": request})

@app.get("/chat", response_class=HTMLResponse)
async def get_chat_page(request: Request):
    """Chat interface page - requires authentication (handled by frontend)"""
    return templates.TemplateResponse("chat.html", {"request": request})

@app.get("/profile", response_class=HTMLResponse)
async def get_profile_page(request: Request):
    """Profile settings page - requires authentication (handled by frontend)"""
    return templates.TemplateResponse("profile.html", {"request": request})

@app.get("/scheduler", response_class=HTMLResponse)
async def get_scheduler_page(request: Request):
    """Auto-sync scheduler monitoring page"""
    return templates.TemplateResponse("scheduler.html", {"request": request})

# Legacy endpoints for backward compatibility
@app.post("/api/chat")
async def legacy_chat_redirect():
    """Legacy chat endpoint - redirects to new multi-store chat"""
    raise HTTPException(
        status_code=301,
        detail="Chat endpoint moved to /assistants/chat. Please use the new multi-store chat interface."
    )

@app.get("/api/cron/status")
async def get_cron_status():
    """Get cron-based auto-sync status"""
    try:
        from cron_sync_status import get_cron_sync_status
        status = get_cron_sync_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting cron sync status: {str(e)}")

@app.post("/api/cron/trigger")
async def trigger_manual_cron_sync():
    """Manually trigger cron-based sync (admin only)"""
    try:
        # Run sync in background to avoid blocking the request
        import threading
        from auto_sync_cron import main as run_cron_sync
        sync_thread = threading.Thread(target=run_cron_sync)
        sync_thread.daemon = True
        sync_thread.start()

        return {"message": "Manual cron sync triggered successfully", "status": "running"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error triggering manual cron sync: {str(e)}")

@app.post("/api/refresh")
async def legacy_refresh_redirect():
    """Legacy refresh endpoint - redirects to new assistant sync"""
    raise HTTPException(
        status_code=301,
        detail="Refresh endpoint moved to /assistants/{assistant_id}/sync. Please use the new assistant management interface."
    )

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
