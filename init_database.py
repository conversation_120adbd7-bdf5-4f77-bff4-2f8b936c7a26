#!/usr/bin/env python3
"""
Database initialization script for Pinecone E-commerce Assistant
This script creates the MySQL database and tables if they don't exist.
"""

import os
import sys
from urllib.parse import quote_plus
import pymysql
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_database_if_not_exists():
    """Create the database if it doesn't exist"""

    # Get database configuration
    db_user = os.getenv("DB_USER", "root")
    db_password = os.getenv("DB_PASSWORD", "")
    db_host = os.getenv("DB_HOST", "localhost")
    db_port = int(os.getenv("DB_PORT", "3306"))
    db_name = os.getenv("DB_NAME", "pinecone_ecommerce")

    print(f"Connecting to MySQL server at {db_host}:{db_port} as {db_user}")

    try:
        # Connect to MySQL server (without specifying database)
        connection = pymysql.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_password,
            charset='utf8mb4'
        )

        print("✅ Connected to MySQL server successfully!")

        with connection.cursor() as cursor:
            # Drop database if it exists (for clean setup)
            cursor.execute(f"DROP DATABASE IF EXISTS `{db_name}`")
            print(f"🗑️  Dropped existing database '{db_name}' if it existed")

            # Create database
            cursor.execute(f"CREATE DATABASE `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ Database '{db_name}' created successfully!")

            # Show databases to confirm
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            db_list = [db[0] for db in databases]

            if db_name in db_list:
                print(f"✅ Confirmed: Database '{db_name}' exists in MySQL")
            else:
                print(f"❌ Error: Database '{db_name}' not found after creation")
                return False

        connection.close()
        return True

    except Exception as e:
        print(f"❌ Error connecting to MySQL: {str(e)}")
        print("\nTroubleshooting tips:")
        print("1. Make sure MySQL server is running")
        print("2. Check your database credentials in .env file")
        print("3. Ensure the user has permission to create databases")
        return False

def initialize_tables():
    """Initialize database tables using SQLAlchemy"""
    try:
        from database.session import test_connection, init_db

        print("\nTesting database connection with SQLAlchemy...")
        if not test_connection():
            print("❌ SQLAlchemy connection test failed")
            return False

        print("Creating database tables...")
        if init_db():
            print("✅ Database tables created successfully!")
            return True
        else:
            print("❌ Failed to create database tables")
            return False

    except Exception as e:
        print(f"❌ Error initializing tables: {str(e)}")
        return False

def main():
    """Main initialization function"""
    print("🚀 Initializing Pinecone E-commerce Assistant Database")
    print("=" * 60)

    # Step 1: Create database
    print("\n📋 Step 1: Creating MySQL database...")
    if not create_database_if_not_exists():
        print("❌ Database creation failed. Exiting.")
        sys.exit(1)

    # Step 2: Create tables
    print("\n📋 Step 2: Creating database tables...")
    if not initialize_tables():
        print("❌ Table creation failed. Exiting.")
        sys.exit(1)

    print("\n🎉 Database initialization completed successfully!")
    print("\nNext steps:")
    print("1. Run the application: python main.py")
    print("2. Access the web interface at: http://localhost:8000")
    print("3. Register a new user account")
    print("4. Connect your Shopify stores")

if __name__ == "__main__":
    main()
